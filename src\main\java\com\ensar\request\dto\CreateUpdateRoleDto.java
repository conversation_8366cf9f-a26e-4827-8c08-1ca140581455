package com.ensar.request.dto;

import com.ensar.entity.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

@Schema(name = "CreateUpdateRoleDto", description = "Parameters required to create/update role\"")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateRoleDto {

    @Schema(description = "Role permission", required = true)
    @NotNull(message = "Role Permission is required")
    private Role.RolePermission rolePermission;

    @Schema(description = "Role Description", required = true)
    @NotBlank(message = "Role Description is required")
    private String roleDescription;

    @Schema(description ="Role Name", required = true)
    @NotBlank(message = "Role Name is required")
    @Size(max = 100)
    private String roleName;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;

}
