-- Drop calendar_events table if it exists
DROP TABLE IF EXISTS `calendar_events`;

-- Re-create the calendar_events table
CREATE TABLE `calendar_events`
(
    `id`                     CHAR(36)     NOT NULL,
    `title`                  VARCHAR(100) NOT NULL,
    `description`            TEXT         NOT NULL,
    `user_id`                CHAR(36)     NOT NULL,
    `lead_id`                CHAR(36)     NOT NULL,
    `start_date`             TIMESTAMP    NOT NULL,
    `end_date`               TIMESTAMP    NOT NULL,
    `all_day`                BOOLEAN      NOT NULL DEFAULT FALSE,
    `color`                  VARCHAR(20)  NULL,
    `additional_note`        VARCHAR(255) NULL,
    `organization_id`        CHAR(36) NOT NULL,
    `created_date_time`      TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` TIMESTAMP    NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (`lead_id`) REFERENCES `leads`(`id`) ON DELETE CASCADE,
    CONSTRAINT fk_calendar_events_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;
