package com.ensar.request.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import jakarta.validation.constraints.NotBlank;

@Getter
@Setter
public class CreateUpdateCompanyDto {
    @NotBlank(message = "Company name is required")
    private String name;

    private String website;
    private String region;
    private Integer empCount;

    @NotBlank(message = "Industry ID is required")
    private String industryId;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}
