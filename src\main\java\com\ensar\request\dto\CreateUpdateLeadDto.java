package com.ensar.request.dto;

import com.ensar.entity.Organization;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.PastOrPresent;

import java.sql.Date;

import com.ensar.entity.Lead.LeadStatus;

@Schema(description = "Parameters required to create/update lead")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateLeadDto {

    @Schema(description = "First Name", required = true)
    @NotBlank(message = "first Name is required")
    @Size(max = 30)
    private String firstname;

    @Schema(description = "Last Name", required = true)
    @NotBlank(message = "Last Name is required")
    @Size(max = 30)
    private String lastname;

    @Schema(description = "Lead Industry ID", required = true)
    private String industryId;

    @Schema(description ="Lead Designation", required = true)
    @NotBlank(message = "Lead Designation is required")
    private String designationId;

    @Schema(description = "Lead Email")
    @Email(message = "Lead Email should be valid")
    @Size(max = 50)
    private String email;

    @Schema(description = "Lead Phone Number")
    private String phoneNumber;

    @Schema(description ="Lead Status")
    private LeadStatus status;

    @Schema(description = "Lead Date")
    private Date leaddate;

    @Schema(description = "LinkedIn URL")
    @Size(max = 255)
    private String linkedin;

    @Schema(description = "Website URL")
    @Size(max = 255)
    private String website;

    @Schema(description = "Region")
    @Size(max = 50)
    private String region;

    @Schema(description = "Employee Count")
    private String empCount;

    @Schema(description = "Verified Status", required = true)
    @NotNull(message = "Verified status is required")
    private Boolean verified;

    @Schema(description = "Message Sent Status", required = true)
    @NotNull(message = "Message Sent status is required")
    private Boolean messageSent;

    @Schema(description = "Comments")
    private String comments;

    @Schema(description = "Sent By User ID", required = true)
    private String sentById;

    @Schema(description = "Organization User ID", required = true)
    private String organizationId;

    @Schema(description = "Lead Draft Status")
    private Boolean draftStatus;
}
