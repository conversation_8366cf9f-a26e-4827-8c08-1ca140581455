
package com.ensar.request.dto;

import com.ensar.entity.Lead;
import com.ensar.entity.LeadTask;
import com.ensar.entity.User;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

import java.sql.Date;
import java.sql.Timestamp;

@Schema(description =  "Parameters required to create/update user task")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateUserTaskDto {

    @Schema(description = "Lead ID", required = true)
    private String leadId;
    @Schema(description = "User ID", required = true)
    private String userId;

    @Schema(description = "Task Name", required = true)
    @NotBlank(message = "Task Name is required")
    @Size(max = 100)
    private String name;

    @Schema(description = "Description", required = true)
    @NotBlank(message = "Description is required")
    private String description;



    @Schema(description =  "start Date", required = true)
    @NotNull(message = "start Date required")
    private Timestamp startDate;

    @Schema(description =  "end Date", required = true)
    @NotNull(message = "end Date is required")
    private Timestamp endDate;

    @Schema(description ="Priority", required = true)
    @NotBlank(message = "Priority is required")
    private String priority;

    @Schema(description = "Stage", required = true)
    @NotBlank(message = "Stage is required")
    private String status;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}
