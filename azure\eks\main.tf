# Configure the Azure provider
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}

provider "azurerm" {
  features {}
}

variable "location" {
  description = "The Azure region to deploy resources."
  default     = "East US"
}

variable "resource_group_name" {
  description = "The name of the existing resource group."
  default     = "ai2"
}

resource "azurerm_kubernetes_cluster" "aks_cluster" {
  name                = "ensar-aks-cluster"
  location            = var.location
  resource_group_name = var.resource_group_name
  dns_prefix          = "ensarakscluster"

  default_node_pool {
    name       = "default"
    node_count = 3
    vm_size    = "Standard_DS2_v2"
  }

  identity {
    type = "SystemAssigned"
  }

  network_profile {
    network_plugin     = "azure"
    load_balancer_sku  = "standard"  # Use Standard SKU for public IP support
    outbound_type      = "loadBalancer"
    service_cidr       = "10.0.0.0/16"
    dns_service_ip     = "*********"
  }
}

output "kube_config" {
  description = "Kubernetes Config"
  value       = azurerm_kubernetes_cluster.aks_cluster.kube_config_raw
  sensitive   = true
}
