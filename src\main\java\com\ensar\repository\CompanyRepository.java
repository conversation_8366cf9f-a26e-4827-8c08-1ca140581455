package com.ensar.repository;

import com.ensar.entity.Company;
import com.ensar.entity.Project;
import com.ensar.response.dto.CompanyResponseDto;
import com.ensar.response.dto.LeadResponseDto;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CompanyRepository extends JpaRepository<Company, String> {

	Optional<LeadResponseDto> findByName(String string);

	List<CompanyResponseDto> findByOrganizationId(String organizationId);
}
