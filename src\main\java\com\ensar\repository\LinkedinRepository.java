package com.ensar.repository;


import com.ensar.entity.Linkedin;
import com.ensar.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface LinkedinRepository extends JpaRepository<Linkedin, String> {

    Page<Linkedin> findByOrganizationId(String organizationId, Pageable pageable);

    List<Linkedin> findByOrganizationId(String organizationId);
}
