package com.ensar.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.sql.Date;
import java.util.List;

@Entity(name = "Lead")
@Table(name = "leads")
@JsonIgnoreProperties(value = {"Lead", "hibernateLazyInitializer"})
@Data
@EqualsAndHashCode(callSuper = true)
//@JsonIgnoreProperties(ignoreUnknown = true)
public class Lead extends BaseEntity {

    @Column(name = "first_name")
    private String firstname;

    @Column(name = "last_name")
    private String lastname;

    @OneToOne
    @JoinColumn(name = "designation_id", nullable = false)
    private Designation designation;

    @Column(name = "email", length = 50, unique = true)
    private String email;

    @Column(name = "phone_number", length = 10, unique = true)
    private String phoneNumber;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private LeadStatus status = LeadStatus.New;

    @Column(name = "lead_date")
    private Date leaddate;

    @Column(name = "linkedin", length = 255, unique = true)
    private String linkedin;

    @Column(name = "website", length = 255)
    private String website;

    @Column(name = "region", length = 50)
    private String region;

    @Column(name = "emp_count")
    private String empCount;

    @Column(name = "verified", nullable = false)
    private Boolean verified = false;

    @Column(name = "message_sent", nullable = false)
    private Boolean messageSent = false;

    @Column(name = "comments", columnDefinition = "TEXT")
    private String comments;

    @ManyToOne
    @JoinColumn(name = "sent_by", nullable = false)
    private User sentBy;

    @ManyToOne
    @JoinColumn(name = "organization_id", nullable = false)
    private Organization organization;

    @OneToMany(mappedBy = "lead", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<LeadReply> replies;


    @Column(name = "draft_status", nullable = false, columnDefinition = "TINYINT(1) DEFAULT 0")
    private Boolean draftStatus = false;  // Default to false (not a draft)

    @OneToOne
    @JoinColumn(name = "industry_id", referencedColumnName = "id")
    private Industry industry;

    public enum LeadStatus {
        New,
        Contacted,
        Qualified,
        Lost,
        Won,
        Unqualified
    }
}