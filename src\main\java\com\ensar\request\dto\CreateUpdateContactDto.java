package com.ensar.request.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.sql.Date;

@Schema(description = "Parameters required to create/update contact")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateContactDto {

    @Schema(description = "First name of the contact", required = true)
    @NotBlank(message = "First Name is required")
    @Size(max = 50, message = "First Name cannot exceed 50 characters")
    private String firstName;

    @Schema(description = "Last Name of the contact", required = true)
    @NotBlank(message = "Last Name is required")
    @Size(max = 50, message = "Last Name cannot exceed 50 characters")
    private String lastName;

    @Schema(description = "Email of the contact", required = true)
    @NotBlank(message = "Email is required")
    @Email(message = "Email should be valid")
    @Size(max = 100, message = "Email cannot exceed 100 characters")
    private String email;

    @Schema(description = "Enrollment date of the contact", required = true)
    @NotNull(message = "Enrollment Date is required")
    private Date enrollmentDate;

    @Schema(description = "Disabled status of the contact")
    private boolean disabled = false;
}
