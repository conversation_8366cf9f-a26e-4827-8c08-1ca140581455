package com.ensar.repository;

import com.ensar.entity.Project;
import com.ensar.entity.Role;
import com.ensar.entity.Target;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface RoleRepository extends JpaRepository<Role, String> {
    List<Role> findByOrganizationId(String organizationId);
    Page<Role> findByOrganizationId(String orgId, Pageable pageable);
    List<Role> findByRolePermission(Role.RolePermission rolePermission);

    Role findByRoleName(String roleName);

}
