package com.ensar.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ensar.entity.Contact;
import com.ensar.repository.ContactRepository;
import com.ensar.request.dto.CreateUpdateContactDto;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class ContactService {

    private final ContactRepository contactRepository;

    @Autowired
    public ContactService(ContactRepository contactRepository) {
        this.contactRepository = contactRepository;
    }

    public Contact createOrUpdateContact(Optional<String> contactId, CreateUpdateContactDto contactDto) {
        Contact contact;
        if (contactId.isPresent()) {
            contact = contactRepository.findById(contactId.get())
                .orElseThrow(() -> new RuntimeException("Contact with id " + contactId.get() + " not found"));
        } else {
            contact = new Contact();
        }

        contact.setFirstName(contactDto.getFirstName());
        contact.setLastName(contactDto.getLastName());
        contact.setEmail(contactDto.getEmail());
        contact.setEnrollmentDate(contactDto.getEnrollmentDate());
        contact.setDisabled(contactDto.isDisabled());

        return contactRepository.save(contact);
    }

    public Contact getContactById(String contactId) {
        return contactRepository.findById(contactId)
                .orElseThrow(() -> new RuntimeException("Contact with id " + contactId + " not found"));
    }

    public List<Contact> getAllContacts() {
        return contactRepository.findAll();
    }

    public void deleteContact(String contactId) {
        Contact contact = contactRepository.findById(contactId)
                .orElseThrow(() -> new RuntimeException("Contact with id " + contactId + " not found"));
        contactRepository.delete(contact);
    }
}
