package com.ensar.request.dto;

import com.ensar.entity.Lead;
import com.ensar.entity.User;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

import java.sql.Date;
import java.sql.Timestamp;

@Schema( description = "Parameters required to create/update a calendar event")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true) // Ignores unknown properties like `id` if not explicitly set
public class CreateUpdateCalendarEventDto {

    @Schema(description = "Lead ID", required = true)
    private Lead lead; // Use the Lead's ID instead of the entire entity

    @Schema(description = "User ID", required = true)
    private User user;

    @Schema(description = "User Title", required = true)
    @NotBlank(message = "Title is required")
    @Size(max = 100)
    private String title;

    @Schema(description = "Description", required = true)
    @NotBlank(message = "Description is required")
    private String description;

    @Schema(description = "Start Date and Time", required = true)
    @NotNull(message = "Start date is required")
    private Timestamp startDate;

    @Schema(description = "End Date and Time", required = true)
    @NotNull(message = "End date is required")
    private Timestamp endDate;

    @Schema(description = "All Day Event", required = true)
    @NotNull(message = "All day field is required")
    private Boolean allDay;

    @Schema(description = "Event Color", required = true)
//    @NotBlank(message = "Color is required")
    @Size(max = 20)
    private String color;

    @Schema(description = "Additional Note")
    private String additionalNote;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}
