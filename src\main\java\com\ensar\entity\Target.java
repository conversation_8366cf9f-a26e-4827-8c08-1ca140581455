package com.ensar.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.persistence.*;

import java.sql.Date;

@Entity(name = "targets")
@Data
@EqualsAndHashCode(callSuper = true)
public class Target extends BaseEntity {

    @Column(name = "account_name", nullable = false)
    private String accountName;

    @Column(name = "connections_count", nullable = false)
    private Integer connectionsCount;

    @ManyToOne
    @JoinColumn(name = "handled_by", nullable = false)
    private User handledBy;

    @Column(name = "no_of_leads_identified", nullable = false)
    private Integer noOfLeadsIdentified;

    @Column(name = "connections_sent", nullable = false)
    private Integer connectionsSent;

    @Column(name = "messages_sent", nullable = false)
    private Integer messagesSent;

    @Column(name = "follow_ups", nullable = false)
    private Integer followUps;

    @Column(name = "response_received", nullable = false)
    private String responseReceived;

    @Column(name = "meetings_scheduled", nullable = false)
    private Integer meetingsScheduled;

    @Column(name = "in_mail_count", nullable = false)
    private Integer inMailCount;

    @Column(name = "postings", nullable = false)
    private Integer postings;

    @Column(name = "status", nullable = false, length = 10)
    @Enumerated(EnumType.STRING)
    private Target.Status status ;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;

    @Column(name = "created_date", nullable = false)
    private Date createdDate;

    public enum Status {
        Active,
        InActive,
        OnHold
    }
}
