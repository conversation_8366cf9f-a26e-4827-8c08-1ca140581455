package com.ensar.request.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

@Schema(description =  "Parameters required to create/update organization")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateOrgDto {

    @Schema(description = "Organization Name", required = true)
    @NotBlank(message = "Organization Name is required")
    @Size(max = 50)
    private String name;

    @Schema(description = "Organization Description", required = true)
    @NotBlank(message = "Organization Description is required")
    @Size(max = 500)
    private String description;

    @Schema(description = "Organization Email Domain", required = true)
    @NotBlank(message = "Organization Email Domain is required")
    @Size(max = 50)
    private String domain;

    @Schema(description ="Organization Logo")
    private String logoImgSrc;

    @Schema(description = "disabled")
    private boolean disabled=false;

}
