package com.ensar.request.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;

import java.sql.Date;

@Schema(description = "Parameters required to create/update lead response")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateLeadResponseDto {


//    @ApiModelProperty(notes = "Lead ID", required = true)
//    @NotBlank(message = "Lead ID is required")
//    private String leadId;

    @Schema(description = "Reply ID", required = true)
    @NotBlank(message = "Reply ID is required")
    private String replyId;

    @Schema(description = "Response Text", required = true)
    @NotBlank(message = "Response Text is required")
    private String response;

    @Schema(description = "Respond At Timestamp", required = true)
    @NotBlank(message = "Respond At is required")
    private Date respondAt;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}
