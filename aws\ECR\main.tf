provider "aws" {
  region = "us-east-1" 
}

resource "aws_ecr_repository" "crm_api_repo" {
  name = "crm-api"  
  image_tag_mutability = "MUTABLE"  
  image_scanning_configuration {
    scan_on_push = true  
  }

  tags = {
    Name        = "crm-api-repo"
    Environment = "production"
  }
}

output "ecr_repo_url" {
  value       = aws_ecr_repository.crm_api_repo.repository_url
  description = "URL of the ECR repository"
}
