package com.ensar.mapper;

import com.ensar.entity.Company;
import com.ensar.entity.Industry;
import com.ensar.request.dto.CreateUpdateCompanyDto;
import com.ensar.response.dto.CompanyResponseDto;
import com.ensar.response.dto.IndustryResponseDto;

public class CompanyMapper {

    public static CompanyResponseDto toDto(Company company) {
        CompanyResponseDto dto = new CompanyResponseDto();
        dto.setId(company.getId());
        dto.setName(company.getName());
        dto.setWebsite(company.getWebsite());
        dto.setRegion(company.getRegion());
        dto.setEmpcount(company.getEmpCount());

        IndustryResponseDto industryDto = new IndustryResponseDto();
        Industry industry = company.getIndustry();
        if (industry != null) {
            industryDto.setId(industry.getId());
            industryDto.setName(industry.getName());
        }
        dto.setIndustry(industryDto);

        return dto;
    }

    public static Company toEntity(CreateUpdateCompanyDto dto, Industry industry) {
        Company company = new Company();
        company.setName(dto.getName());
        company.setWebsite(dto.getWebsite());
        company.setRegion(dto.getRegion());
        company.setEmpCount(dto.getEmpCount());
        company.setIndustry(industry);
        return company;
    }
}
