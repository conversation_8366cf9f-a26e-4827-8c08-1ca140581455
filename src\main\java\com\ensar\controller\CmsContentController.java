package com.ensar.controller;

import com.ensar.entity.CmsContent;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateCmsContentDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.CmsContentService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/cms-contents")
@Tag(name = "CmsContent Management") // Class-level Tag annotation
public class CmsContentController {

    private final CmsContentService cmsContentService;

    @Autowired
    public CmsContentController(CmsContentService cmsContentService) {
        this.cmsContentService = cmsContentService;
    }

    // Get CmsContent by ID
    @GetMapping("/{id}")
    public ResponseEntity<CmsContent> getCmsContentById(@PathVariable String id) {
        CmsContent cmsContent = cmsContentService.getCmsContentById(id);
        return ResponseEntity.ok(cmsContent);
    }

    // Get all CmsContents

    @GetMapping("/")
    public ResponseEntity<Map<String, List<CmsContent>>> getAllCmsContent(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<CmsContent> cmsContent = cmsContentService.getAllCmsContent(orgId);
        System.out.println("CMS Content: " + cmsContent);  // Add logging to check if data is correct
        Map<String, List<CmsContent>> response = new HashMap<>();
        response.put("cmsContent", cmsContent);
        return ResponseEntity.ok(response);
    }

    // Create new CmsContent
    @PostMapping("/")
    public ResponseEntity<CmsContent> createCmsContent(@Valid @RequestBody CreateUpdateCmsContentDto createUpdateCmsContentDto, @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        CmsContent cmsContent = cmsContentService.createOrUpdateCmsContent(Optional.empty(), createUpdateCmsContentDto,organization);
        return ResponseEntity.ok(cmsContent);
    }

    // Update an existing CmsContent
    @PutMapping("/{id}")
    public ResponseEntity<CmsContent> updateCmsContent(@PathVariable String id,
                                                       @Valid @RequestBody CreateUpdateCmsContentDto createUpdateCmsContentDto,
                                                       @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        CmsContent cmsContent = cmsContentService.createOrUpdateCmsContent(Optional.of(id), createUpdateCmsContentDto,organization);
        return ResponseEntity.ok(cmsContent);
    }

    // Delete CmsContent by ID
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCmsContent(@PathVariable String id) {
        cmsContentService.deleteCmsContent(id);
        return ResponseEntity.ok().build();
    }

    // Import CmsContents from CSV file
    @PostMapping("/import")
    public ResponseEntity<Void> importCmsContent(@RequestParam("file") MultipartFile file) throws IOException {
        cmsContentService.importCmsContent(file.getInputStream());
        return ResponseEntity.ok().build();
    }

    // Export CmsContents to CSV file
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportCmsContent() throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        cmsContentService.exportCmsContent(outputStream);
        byte[] bytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename("cms_contents.csv")
                .build();
        headers.setContentDisposition(contentDisposition);

        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);
    }

    // Upload an image for CmsContent
    @PutMapping("/upload-image/{id}")
    public ResponseEntity<?> uploadCmsContentImage(@PathVariable String id, @RequestParam("image") MultipartFile image) {
        try {
            Optional<CmsContent> updatedCmsContent = cmsContentService.saveImageAsBlob(id, image.getBytes());
            if (updatedCmsContent.isPresent()) {
                return ResponseEntity.ok(updatedCmsContent.get());
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body("CmsContent not found with id: " + id);
            }
        } catch (IOException e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body("Failed to upload image");
        }
    }

    // Retrieve image for CmsContent
    @GetMapping("/{id}/image")
    public ResponseEntity<byte[]> getCmsContentImage(@PathVariable String id) {
        Optional<CmsContent> cmsContentOptional = cmsContentService.getImageData(id);
        if (cmsContentOptional.isPresent() && cmsContentOptional.get().getCoverUrlData() != null) {
            byte[] imageData = cmsContentOptional.get().getCoverUrlData();
            MediaType mediaType = determineMediaType(imageData);
            return ResponseEntity.ok()
                    .contentType(mediaType)
                    .body(imageData);
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }

    // Determine the media type based on the image data
    private MediaType determineMediaType(byte[] imageData) {
        if (imageData.length > 4 && imageData[0] == (byte) 0xFF && imageData[1] == (byte) 0xD8) {
            return MediaType.IMAGE_JPEG;
        } else if (imageData.length > 4 && imageData[0] == (byte) 0x89 && imageData[1] == (byte) 0x50) {
            return MediaType.IMAGE_PNG;
        } else if (imageData.length > 4 && imageData[0] == (byte) 0x47 && imageData[1] == (byte) 0x49) {
            return MediaType.IMAGE_GIF;
        } else {
            return MediaType.APPLICATION_OCTET_STREAM;
        }
    }
}

