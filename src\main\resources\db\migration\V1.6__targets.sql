DROP TABLE IF EXISTS `targets`;

CREATE TABLE `targets`
(
    `id`                   char(36)     NOT NULL,
    `account_name`         VARCHAR(100) NOT NULL,
    `connections_count`   INT NOT NULL,
    `status` ENUM('Active', 'InActive', 'OnHold') DEFAULT 'InActive',
    `handled_by`          CHAR(36) NOT NULL,
    `no_of_leads_identified` INT NOT NULL,
    `connections_sent` INT NOT NULL,
    `messages_sent` INT NOT NULL,
    `follow_ups` INT NOT NULL,
    `response_received` VARCHAR(10) NOT NULL,
    `meetings_scheduled` INT NOT NULL,
    `in_mail_count` INT NOT NULL,
    `postings` INT NOT NULL,
    `created_date` DATE NOT NULL,
    `organization_id` CHAR(36) NOT NULL,
    `created_date_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp NULL DEFAULT NULL,
    CONSTRAINT fk_target_handled_by FOR<PERSON><PERSON><PERSON> KEY (handled_by) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_target_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

