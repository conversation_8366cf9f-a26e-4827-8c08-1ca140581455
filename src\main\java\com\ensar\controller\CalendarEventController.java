package com.ensar.controller;

import com.ensar.entity.CalendarEvent;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateCalendarEventDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.CalendarEventService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/calendar")
@Tag(name = "Calendar Event Management")
public class CalendarEventController {

    private final CalendarEventService calendarEventService;

    @Autowired
    public CalendarEventController(CalendarEventService calendarEventService) {
        this.calendarEventService = calendarEventService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<CalendarEvent> getEventById(@PathVariable String id) {
        CalendarEvent event = calendarEventService.getEventById(id);
        return ResponseEntity.ok(event);
    }

    @GetMapping("/")
    public ResponseEntity<Map<String, List<CalendarEvent>>> getAllEvents(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<CalendarEvent> eventList = calendarEventService.getAllEvents(orgId);
        Map<String, List<CalendarEvent>> response = new HashMap<>();
        response.put("events", eventList);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    public ResponseEntity<CalendarEvent> createEvent(@Valid @RequestBody CreateUpdateCalendarEventDto createUpdateCalendarEventDto, @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        CalendarEvent event = calendarEventService.createOrUpdateEvent(Optional.empty(), createUpdateCalendarEventDto,organization);
        return ResponseEntity.ok(event);
    }

    @PutMapping("/{id}")
    public ResponseEntity<CalendarEvent> updateEvent(
            @PathVariable String id,
            @Valid @RequestBody CreateUpdateCalendarEventDto createUpdateCalendarEventDto,
            @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        CalendarEvent event = calendarEventService.createOrUpdateEvent(Optional.of(id), createUpdateCalendarEventDto,organization);
        return ResponseEntity.ok(event);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEvent(@PathVariable String id) {
        calendarEventService.deleteEvent(id);
        return ResponseEntity.ok().build();
    }
}
