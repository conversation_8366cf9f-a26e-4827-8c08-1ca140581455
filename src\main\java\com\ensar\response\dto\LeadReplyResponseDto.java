package com.ensar.response.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.sql.Date;
import java.util.List;

@Accessors(chain = true)
@Setter
@Getter
public class LeadReplyResponseDto {
    private String id;
    private String leadId;
    private String replyText;
    private UserResponseDto replier;
    private Date replyAt;
    private List<LeadResponseResponseDto> leadresponses;
}
