package com.ensar.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.sql.Date;
import java.sql.Timestamp;

@Entity(name = "lead_tasks")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeadTask extends BaseEntity {

  @Column(name = "task_description", nullable = false, columnDefinition = "TEXT")
  private String taskDescription;

  @ManyToOne
  @JsonBackReference
  @JsonIgnoreProperties
// @JsonIgnoreProperties({"leadTasks"})
  @JoinColumn(name = "lead_id", nullable = false)
  private Lead lead;

  @Column(name = "task_name", nullable = false, length = 30)
  private String taskName;


  @ManyToOne
  @JoinColumn(name = "assigned_to", nullable = false)
  private User assignedTo;

  @Column(name = "start_date", nullable = true)
  private Timestamp startDate;

  @Column(name = "end_date", nullable = true)
  private Timestamp endDate;

  @Column(name = "status", nullable = false)
  @Enumerated(EnumType.STRING)
  private TaskStatus status;

  @OneToOne
  @JoinColumn(name = "organization_id", referencedColumnName = "id")
  private Organization organization;

  @Column(name = "priority", nullable = false, length = 10)
  @Enumerated(EnumType.STRING)
  private UserTask.Priority priority;


  public enum TaskStatus {
    Pending,
    InProgress,
    Completed
  }
  public enum Priority {
    Low,
    Medium,
    High
  }
}
