package com.ensar.controller;

import com.ensar.entity.Industry;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateIndustryDto;
import com.ensar.response.dto.IndustryResponseDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.IndustryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/industries")
@CrossOrigin(origins = "*", maxAge = 3600)
public class IndustryController {

    @Autowired
    private IndustryService industryService;

    @GetMapping
    public ResponseEntity<Map<String, List<Industry>>> getAllIndustries(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<Industry> industries = industryService.getAllIndustries(orgId);
        Map<String, List<Industry>> response = new HashMap<>();
        response.put("industries", industries);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public IndustryResponseDto getIndustryById(@PathVariable String id) {
        return industryService.getIndustryById(id);
    }

    @PostMapping
    public IndustryResponseDto createIndustry(@RequestBody CreateUpdateIndustryDto industry,
                                              @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        return industryService.saveIndustry(industry,organization);
    }

    @PutMapping("/{id}")
    public IndustryResponseDto updateIndustry(@PathVariable String id,
                                              @RequestBody CreateUpdateIndustryDto industry,
                                              @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        industry.setId(id);
        return industryService.saveIndustry(industry,organization);
    }

    @DeleteMapping("/{id}")
    public void deleteIndustry(@PathVariable String id) {
        industryService.deleteIndustry(id);
    }
}
