package com.ensar.repository;

import com.ensar.entity.CmsContent;
import com.ensar.entity.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CmsContentRepository extends JpaRepository<CmsContent, String> {

    // You can add custom query methods here if needed in the future
    List<CmsContent> findByOrganizationId(String organizationId);
}
