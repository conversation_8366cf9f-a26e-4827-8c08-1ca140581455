    DROP TABLE IF EXISTS `linkedins`;

    CREATE TABLE `linkedins`
    (
        `id`                   CHAR(36) NOT NULL,
        `account_name`         VARCHAR(50) NOT NULL,
        `email`                VARCHAR(254) NOT NULL,
        `password`             VARCHAR(20) NOT NULL,
        `designation`          VARCHAR(25) NOT NULL,
        `country`              VARCHAR(20) NOT NULL,
        `connections_count`    INT DEFAULT 0,
        `status` ENUM('Active', 'InActive', 'OnHold') DEFAULT 'InActive',
        `handled_by`           CHAR(36) NOT NULL,
        `organization_id` CHAR(36) NOT NULL,
        `created_date_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
        `last_updated_date_time` timestamp NULL DEFAULT NULL,
        CONSTRAINT fk_linkedin_handled_by <PERSON>OR<PERSON><PERSON><PERSON> KEY (handled_by) REFERENCES users(id) ON DELETE CASCADE,
        PRIMAR<PERSON> (`id`),
        CONSTRAINT fk_linkedin_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
    ) ENGINE = InnoDB
      DEFAULT CHARSET = utf8;
