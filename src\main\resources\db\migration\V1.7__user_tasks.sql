DROP TABLE IF EXISTS `user_tasks`;

CREATE TABLE `user_tasks`
(
    `id`                   char(36)     NOT NULL,
    `name`                 VARCHAR(100) NOT NULL,
    `user_id`              CHAR(36)    NOT NULL,
    `lead_id`              CHAR(36)    NOT NULL,
    `description`          TEXT         NOT NULL,
    `start_date`             TIMESTAMP         NOT NULL,
    `end_date`             TIMESTAMP         NOT NULL,
    `organization_id` CHAR(36) NOT NULL,
    `priority`             ENUM('Low' , 'Medium','High') NOT NULL DEFAULT 'Medium',
    `status`                ENUM('Pending', 'InProgress', 'Completed') NOT NULL DEFAULT 'Pending',
    `created_date_time`    TIMESTAMP    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` TIMESTAMP NULL DEFAULT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id)  ON DELETE CASCADE,
    FOREIG<PERSON> KEY (lead_id) REFERENCES leads(id)  ON DELETE CASCADE,
    PRIMARY KEY (`id`),
    CONSTRAINT fk_usertask_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;








