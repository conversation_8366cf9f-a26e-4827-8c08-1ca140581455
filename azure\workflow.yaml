
# Docs for the Azure Web Apps Deploy action: https://go.microsoft.com/fwlink/?linkid=2134798
# More GitHub Actions for Azure: https://go.microsoft.com/fwlink/?linkid=2135048

name: Azure App Service - ensar-crm-springboot(Production), Build and deploy Spring app

on:
  push:
    branches:
      - main

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    # checkout the repo
    - name: 'Checkout Github Action'
      uses: actions/checkout@master

    - name: Set up Java version
      uses: actions/setup-java@v1
      with:
        java-version: '17'

    - name: Set up Gradle
      uses: gradle/gradle-build-action@v2
      with:
        gradle-version: '7.3.3' # Specify the desired Gradle version

    - name: Make gradlew executable
      run: chmod +x gradlew

    - name: Build with Gradle (skip tests)
      run: ./gradlew build -x test

    - name: Run Azure webapp deploy action using publish profile credentials
      uses: azure/webapps-deploy@v2
      with:
        app-name: ensar-crm-springboot
        slot-name: Production
        publish-profile: ${{ secrets.AZUREAPPSERVICE_PUBLISHPROFILE_4664769819B4423C8D2780C3A2CE4DBD  }}
        package: ${{ github.workspace }}/build/libs/demo-0.0.1-SNAPSHOT.jar