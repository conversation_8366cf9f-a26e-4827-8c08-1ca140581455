package com.ensar.entity;


import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.persistence.*;

@Entity(name = "projects")
@Data
@EqualsAndHashCode(callSuper = true)
public class Project extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description", nullable = false)
    private String description;

    @OneToOne
    @JoinColumn(name = "organization", referencedColumnName = "id")
    private Organization organization;
}

