package com.ensar.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.persistence.*;

@Entity(name = "cms_contents")
@Data
@EqualsAndHashCode(callSuper = true)
public class CmsContent extends BaseEntity {

    @Column(name = "title", nullable = false)
    private String title;

    @Column(name = "description")
    private String description;

    @Column(name = "content", nullable = false)
    private String content;

    @Column(name = "meta_title", nullable = false)
    private String metaTitle;

    @Column(name = "meta_tags", nullable = false)
    private String metaTags;

    @Column(name = "meta_description")
    private String metaDescription;

    @Column(name = "meta_keywords", nullable = false)
    private String metaKeywords;

    // New field to store the URL of the cover image
    @Column(name = "cover_url", length = 50)
    private String coverUrl;

    // New field to store the cover image data as binary
    @Lob
    @Column(name = "cover_url_data", columnDefinition = "LONGBLOB")
    private byte[] coverUrlData;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;
}
