package com.ensar.request.dto;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Schema(description = "Parameters required to create/update project")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateProjectDto {

    @Schema(description =  "Project Name", required = true)
    @NotBlank(message = "Project Name is required")
    @Size(max = 100)
    private String name;

    @Schema(description = "Project Description", required = true)
    @NotBlank(message = "Project Description is required")
    private String description;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}
