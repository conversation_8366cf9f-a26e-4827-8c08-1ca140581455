package com.ensar.security;

import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import com.ensar.entity.Organization;
import com.ensar.entity.User;
import com.ensar.entity.Role;

import java.util.Collection;
import java.util.List;

public class EnsarUserDetails implements UserDetails {
    private User user;

    public EnsarUserDetails(User user) {
        this.user = user;
    }

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // Fetching the role's permission from the Role entity
        Role role = user.getRole();
        return List.of(new SimpleGrantedAuthority(role.getRolePermission().name())); // Role permission is used
    }

    @Override
    public String getPassword() {
        return user.getPassword();
    }

    @Override
    public String getUsername() {
        return user.getEmail();
    }

    @Override
    public boolean isAccountNonExpired() {
        return true;
    }

    @Override
    public boolean isAccountNonLocked() {
        return true;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }

    @Override
    public boolean isEnabled() {
        // Check if the user and the associated organization are not disabled
        return !user.isDisabled() && !user.getOrganization().isDisabled();
    }

    public Organization getOrganization() {
        return user.getOrganization();
    }

    public User getUser() {
        return this.user;
    }
}
