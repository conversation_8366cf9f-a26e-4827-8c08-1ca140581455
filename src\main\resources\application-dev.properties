#server.port=8081
JWT_SECRET_KEY=fWnJveXKFm0c/tll7gUOwXfXb4j4CbCvuYeYDEGO02M=
JWT_TTL_MINS=180
DB_SERVER=localhost
DB_PORT=3306
DB_SCHEMA=crm
DB_USE_SSL=false
DB_REQUIRE_SSL=false
DB_USER=root
DB_PASSWORD=root

frontend.url=http://localhost:8081

#spring.mail.host=smtp.gmail.com
#spring.mail.port=587
#spring.mail.username=<EMAIL>
#spring.mail.password=your-email-password
#spring.mail.properties.mail.smtp.auth=true
#spring.mail.properties.mail.smtp.starttls.enable=true


EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.gmail.com
EMAIL_USER_NAME=crmensar
EMAIL_USER_PWD=racoawwsxbyzorhy

AWS_ACCESS_KEY=********************
AWS_SECRET_KEY=8knQ0EbCebTrPttnuYKr/cL7zqkegYQiecw+/j6+
AWS_ACCOUNT_ID=************
AWS_REGION=us-east-1
SQS_QUEUE_URL=https://sqs.us-east-1.amazonaws.com/************/Pmo_Mail.fifo

cloud.aws.credentials.access-key=********************
cloud.aws.credentials.secret-key=8knQ0EbCebTrPttnuYKr/cL7zqkegYQiecw+/j6+
cloud.aws.region.static=us-east-1
cloud.aws.sqs.queue.url=https://sqs.us-east-1.amazonaws.com/************/Pmo_Mail.fifo
app.url.prefix=http://localhost:8081/auth/jwt
spring.redis.host=localhost
#spring.redis.port=6379

# application.properties
server.port=8084
#server.ssl.key-store=classpath:ssl-server.jks
#server.ssl.key-store-password=password
#server.ssl.keyStoreType=PKCS12
#server.ssl.keyAlias=selfsigned_localhost_sslserver

SENDGRID_API_KEY=*********************************************************************

spring.servlet.multipart.enabled=true

spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=racoawwsxbyzorhy
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true
spring.mail.properties.mail.smtp.auth=true
spring.main.allow-bean-definition-overriding=true
sqs.enabled=true
mail.smtp.connectiontimeout=10000
mail.smtp.timeout=10000
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

#spring.mail.host=smtp.gmail.com
#spring.mail.port=587
#spring.mail.username=endtoendspringbootproject
#spring.mail.password=skfdbgopwqwlkjve
#spring.mail.properties.mail.smtp.starttls.enable=true
#spring.mail.properties.mail.smtp.starttls.required=true
#spring.mail.properties.mail.smtp.auth=true


#SMTP_USERNAME=********************
#SMTP_PASSWORD=BGhTO/lt/BpcindsMjNKypvv9gBYbXu6/zdY6z3mR3HU
#EMAIL_FROM=<EMAIL>
#spring.main.allow-bean-definition-overriding=true

# GitHub OAuth2 configuration
#spring.security.oauth2.client.registration.github.client-id=********************
#spring.security.oauth2.client.registration.github.client-secret=8f29490073fd35148fd5c9dcaf48c1b53e4bb6ff
#spring.security.oauth2.client.registration.github.scope=read:user,user:email


spring.security.oauth2.client.registration.github.client-id=********************
spring.security.oauth2.client.registration.github.client-secret=e8bcce8d39590c36689071c06fa0e8d0150abadc
spring.security.oauth2.client.registration.github.scope=read:user,user:email


# Google OAuth2 configuration
#spring.security.oauth2.client.registration.google.client-id=162023636308-7iq3ln116k257oft2srmuh7jspccsgie.apps.googleusercontent.com
#spring.security.oauth2.client.registration.google.client-secret=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

spring.security.oauth2.client.registration.google.client-id=783444898416-fhmqdau889q9duofo8sa60foe5v8i51g.apps.googleusercontent.com
spring.security.oauth2.client.registration.google.client-secret=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