package com.ensar.service;


import com.ensar.entity.Lead;
import com.ensar.entity.Organization;
import com.ensar.entity.Project;
import com.ensar.repository.OrganizationRepository;
import com.ensar.repository.ProjectRepository;
import com.ensar.request.dto.CreateUpdateProjectDto;
import com.ensar.util.JwtTokenUtil;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;

@Service
@Log4j2
@Transactional
public class ProjectService {

    private final ProjectRepository projectRepository;
    private final OrganizationRepository organizationRepository;
    private final JwtTokenUtil util;

    @Autowired
    public ProjectService(ProjectRepository projectRepository, OrganizationRepository organizationRepository, JwtTokenUtil util) {
        this.projectRepository = projectRepository;
        this.organizationRepository=organizationRepository;
        this.util = util;
    }

    public Project getProjectById(String id) {
        Optional<Project> projectOptional = projectRepository.findById(id);
        if (!projectOptional.isPresent())
            throw new RuntimeException("Project with ID " + id + " not found.");

        return projectOptional.get();
    }

    public Page<Project> getProjectsByPagination(String orgId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return projectRepository.findByOrganizationId(orgId, pageable);
    }

    public List<Project> getAllProjects(String orgId) {
        return projectRepository.findByOrganizationId(orgId);
    }

    public Project getProjectByName(String name) {
        return projectRepository.findByName(name);
    }

    public boolean projectExistsByName(String name) {
        return projectRepository.existsByName(name);
    }

    public List<Project> getProjectsByDescriptionContaining(String keyword) {
        return projectRepository.findByDescriptionContaining(keyword);
    }

    public Project createOrUpdateProject(Optional<String> projectId, CreateUpdateProjectDto createUpdateProjectDto, Organization organization) {
        Project project;
        if (projectId.isPresent()) {
            project = projectRepository.findById(projectId.get())
                    .orElseThrow(() -> new RuntimeException("Project with ID " + projectId.get() + " not found"));
        } else {
            project = new Project();
        }



        project.setOrganization(organization);

        project.setName(createUpdateProjectDto.getName());
        project.setDescription(createUpdateProjectDto.getDescription());

        return projectRepository.save(project);
    }

    public void deleteProject(String id) {
        projectRepository.deleteById(id);
    }

    public void importProjects(InputStream inputStream) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        List<Project> projects = new ArrayList<>();
        Map<String, Integer> headerMap = new HashMap<>();

        if ((line = reader.readLine()) != null) {
            String[] headers = line.split(",");
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
        } else {
            throw new IOException("Empty CSV file");
        }

        while ((line = reader.readLine()) != null) {
            String[] fields = line.split(",");

            if (fields.length != headerMap.size()) {
                throw new IOException("Invalid CSV format. Each line must have the same number of fields as the header.");
            }

            Project project = new Project();
            try {
                String name = fields[headerMap.get("name")];
                String description = fields[headerMap.get("description")];

                project.setName(name);
                project.setDescription(description);
            } catch (Exception e) {
                throw new IOException("Error processing line: " + line, e);
            }

            projects.add(project);
        }
        projectRepository.saveAll(projects);
    }

    public void exportProjects(OutputStream outputStream) throws IOException {
        List<Project> projects = projectRepository.findAll();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("Name,Description\n");
        for (Project project : projects) {
            writer.write(String.format("%s,%s\n",
                    project.getName(),
                    project.getDescription()));
        }
        writer.flush();
    }
}
