package com.ensar.service;

import com.ensar.entity.*;
import com.ensar.repository.LeadRepository;
import com.ensar.repository.LeadTaskRepository;
import com.ensar.repository.UserRepository;
import com.ensar.repository.UserTaskRepository;
import com.ensar.request.dto.CreateUpdateCalendarEventDto;
import com.ensar.request.dto.CreateUpdateLeadTaskDto;
import com.ensar.response.dto.LeadTaskResponseDto;
import com.ensar.response.dto.UserResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.sql.Timestamp;
import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
public class LeadTaskService {

  @Autowired
  private LeadTaskRepository leadTaskRepository;

  @Autowired
  private UserTaskRepository userTaskRepository;

  @Autowired
  private LeadRepository leadRepository;

  @Autowired
  private UserRepository userRepository;

  @Autowired
  private CalendarEventService calendarEventService;


  public List<LeadTaskResponseDto> getAllLeadTasks(String orgId) {
    return leadTaskRepository.findByOrganizationId(orgId).stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());
  }

  public Optional<LeadTask> getLeadTaskById(String id) {
    return leadTaskRepository.findById(id);
  }

  public List<LeadTaskResponseDto> getLeadTasksByLeadId(String leadId) {
    List<LeadTask> leadTasks = leadTaskRepository.findByLeadId(leadId);
    return leadTasks.stream().map(this::convertToDto).collect(Collectors.toList());
  }

//
//  public List<LeadTask> getAllLeadTasks() {
//
//    return leadTaskRepository.findAll();
//  }

  public LeadTaskResponseDto convertToDto(LeadTask task) {
    LeadTaskResponseDto dto = new LeadTaskResponseDto();
    dto.setId(task.getId());
    dto.setTaskName(task.getTaskName());
    dto.setTaskDescription(task.getTaskDescription());
    dto.setStartDate(task.getStartDate());
    dto.setEndDate(task.getEndDate());
    dto.setStatus(task.getStatus().name());
    dto.setPriority(task.getPriority().name());
    dto.setLeadId(task.getLead().getId());
  if (task.getAssignedTo() != null) {
      UserResponseDto userDto = new UserResponseDto();
      userDto.setId(task.getAssignedTo().getId());
      userDto.setFirstName(task.getAssignedTo().getFirstName());
      userDto.setLastName(task.getAssignedTo().getLastName());
      userDto.setEmail(task.getAssignedTo().getEmail());
      dto.setAssignedTo(userDto);
    }
    return dto;
  }
  public LeadTask createLeadTask(CreateUpdateLeadTaskDto dto, Organization organization) {
    Lead lead = leadRepository.findById(dto.getLeadId())
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + dto.getLeadId()));

    User assignedTo = userRepository.findById(dto.getAssignedToId())
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found with id " + dto.getAssignedToId()));

    // Generate a single shared ID for both LeadTask and UserTask
    String sharedId = UUID.randomUUID().toString();

    // Create LeadTask
    LeadTask task = new LeadTask();
    task.setId(sharedId);  // Use the shared ID
    task.setLead(lead);
    task.setTaskName(dto.getTaskName());
    task.setTaskDescription(dto.getTaskDescription());
    task.setAssignedTo(assignedTo);
    task.setStartDate(dto.getStartDate());
    task.setEndDate(dto.getEndDate());
    task.setStatus(LeadTask.TaskStatus.valueOf(dto.getStatus()));
    task.setPriority(UserTask.Priority.valueOf(dto.getPriority()));
    task.setOrganization(organization);

    LeadTask savedLeadTask = leadTaskRepository.save(task);

    // Create UserTask with the same ID
    UserTask userTask = new UserTask();
    userTask.setId(savedLeadTask.getId());  // Use the same shared ID
    userTask.setName(savedLeadTask.getTaskName());
    userTask.setUser(assignedTo);
    userTask.setDescription(savedLeadTask.getTaskDescription());
    userTask.setStartDate(savedLeadTask.getStartDate());
    userTask.setEndDate(savedLeadTask.getEndDate());
    userTask.setPriority(UserTask.Priority.valueOf(savedLeadTask.getPriority().name()));
    userTask.setStatus(UserTask.Status.valueOf(savedLeadTask.getStatus().name()));
    userTask.setLead(lead);



    // Save UserTask
    userTaskRepository.save(userTask);


    // Convert Date to Timestamp
    Timestamp startTimestamp = new Timestamp(dto.getStartDate().getTime());
    Timestamp endTimestamp = new Timestamp(dto.getEndDate().getTime());

    String randomColor = generateRandomColor();

    // Create Calendar Event
    CreateUpdateCalendarEventDto calendarEventDto = new CreateUpdateCalendarEventDto();
    calendarEventDto.setTitle(dto.getTaskName());
    calendarEventDto.setDescription(dto.getTaskDescription());
    calendarEventDto.setStartDate(startTimestamp);  // Using Timestamp now
    calendarEventDto.setEndDate(endTimestamp);      // Using Timestamp now
    calendarEventDto.setAllDay(false); // Assuming tasks are not all-day events. You can modify this as needed.
    calendarEventDto.setColor(randomColor); // Set a default color for the calendar event, you can change this as well.
    calendarEventDto.setLead(lead);
    calendarEventDto.setUser(assignedTo);
    // Create or Update Calendar Event in Calendar Table
    calendarEventService.createOrUpdateEvent(Optional.empty(), calendarEventDto,organization);

    return savedLeadTask;
  }
  private String generateRandomColor() {
    Random random = new Random();
    // Generate random RGB values
    int r = random.nextInt(256);
    int g = random.nextInt(256);
    int b = random.nextInt(256);
    // Convert RGB to a hex string
    return String.format("#%02x%02x%02x", r, g, b);
  }


  public LeadTask updateLeadTask(String taskId, CreateUpdateLeadTaskDto dto) {
    LeadTask task = leadTaskRepository.findById(taskId)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead task not found with id " + taskId));

    Lead lead = leadRepository.findById(dto.getLeadId())
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + dto.getLeadId()));

    User assignedTo = userRepository.findById(dto.getAssignedToId())
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found with id " + dto.getAssignedToId()));

    // Update LeadTask
    task.setLead(lead);
    task.setTaskName(dto.getTaskName());
    task.setTaskDescription(dto.getTaskDescription());
    task.setAssignedTo(assignedTo);
    task.setStartDate(dto.getStartDate());
    task.setEndDate(dto.getEndDate());
    task.setStatus(LeadTask.TaskStatus.valueOf(dto.getStatus()));
    task.setPriority(UserTask.Priority.valueOf(dto.getPriority()));

    LeadTask updatedLeadTask = leadTaskRepository.save(task);

    // Update corresponding UserTask using the same ID


    return updatedLeadTask;
  }


  public void deleteLeadTask(String taskId) {
    leadTaskRepository.deleteById(taskId);

    // Also delete the corresponding UserTask
    userTaskRepository.deleteById(taskId);
  }
}



//
//
//package com.ensar.service;
//
//import com.ensar.entity.*;
//import com.ensar.repository.LeadRepository;
//import com.ensar.repository.LeadTaskRepository;
//import com.ensar.repository.UserRepository;
//import com.ensar.repository.UserTaskRepository;
//import com.ensar.request.dto.CreateUpdateLeadTaskDto;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.HttpStatus;
//import org.springframework.stereotype.Service;
//import org.springframework.web.server.ResponseStatusException;
//
//import java.util.List;
//import java.util.Optional;
//import java.util.UUID;
//import java.util.stream.Collectors;
//
//@Service
//public class LeadTaskService {
//
//  @Autowired
//  private LeadTaskRepository leadTaskRepository;
//
//  @Autowired
//  private UserTaskRepository userTaskRepository;
//
//  @Autowired
//  private LeadRepository leadRepository;
//
//  @Autowired
//  private UserRepository userRepository;
//
//  public List<LeadTask> getAllLeadTasks() {
//    // Directly return LeadTask entities
//    return leadTaskRepository.findAll();
//  }
//
//  public Optional<LeadTask> getLeadTaskById(String id) {
//    return leadTaskRepository.findById(id);
//  }
//
//  public List<LeadTask> getLeadTasksByLeadId(String leadId) {
//    // Directly return LeadTask entities filtered by leadId
//    return leadTaskRepository.findByLeadId(leadId);
//  }
//
//  public LeadTask createLeadTask(CreateUpdateLeadTaskDto dto) {
//    Lead lead = leadRepository.findById(dto.getLeadId())
//            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + dto.getLeadId()));
//
//    User assignedTo = userRepository.findById(dto.getAssignedToId())
//            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found with id " + dto.getAssignedToId()));
//
//    // Generate a single shared ID for both LeadTask and UserTask
//    String sharedId = UUID.randomUUID().toString();
//
//    // Create LeadTask
//    LeadTask task = new LeadTask();
//    task.setId(sharedId);  // Use the shared ID
//    task.setLead(lead);
//    task.setTaskName(dto.getTaskName());
//    task.setTaskDescription(dto.getTaskDescription());
//    task.setAssignedTo(assignedTo);
//    task.setDueDate(dto.getDueDate());
//    task.setStatus(LeadTask.TaskStatus.valueOf(dto.getStatus()));
//    task.setPriority(UserTask.Priority.valueOf(dto.getPriority()));
//
//    LeadTask savedLeadTask = leadTaskRepository.save(task);
//
//    // Create UserTask with the same ID
//    UserTask userTask = new UserTask();
//    userTask.setId(savedLeadTask.getId());  // Use the same shared ID
//    userTask.setName(savedLeadTask.getTaskName());
//    userTask.setUser(assignedTo);
//    userTask.setDescription(savedLeadTask.getTaskDescription());
//    userTask.setDueDate(savedLeadTask.getDueDate());
//    userTask.setPriority(UserTask.Priority.valueOf(savedLeadTask.getPriority().name()));
//    userTask.setStage(UserTask.Stage.valueOf(savedLeadTask.getStatus().name()));
//
//    userTask.setLead(lead);
//
//    // Save UserTask
//    userTaskRepository.save(userTask);
//
//    return savedLeadTask;
//  }
//
//  public LeadTask updateLeadTask(String taskId, CreateUpdateLeadTaskDto dto) {
//    LeadTask task = leadTaskRepository.findById(taskId)
//            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead task not found with id " + taskId));
//
//    Lead lead = leadRepository.findById(dto.getLeadId())
//            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + dto.getLeadId()));
//
//    User assignedTo = userRepository.findById(dto.getAssignedToId())
//            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found with id " + dto.getAssignedToId()));
//
//    // Update LeadTask
//    task.setLead(lead);
//    task.setTaskName(dto.getTaskName());
//    task.setTaskDescription(dto.getTaskDescription());
//    task.setAssignedTo(assignedTo);
//    task.setDueDate(dto.getDueDate());
//    task.setStatus(LeadTask.TaskStatus.valueOf(dto.getStatus()));
//    task.setPriority(UserTask.Priority.valueOf(dto.getPriority()));
//
//    LeadTask updatedLeadTask = leadTaskRepository.save(task);
//
//    // Update corresponding UserTask using the same ID
//    UserTask userTask = userTaskRepository.findById(taskId)
//            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "UserTask not found with id " + taskId));
//
//    userTask.setName(updatedLeadTask.getTaskName());
//    userTask.setUser(assignedTo);
//    userTask.setDescription(updatedLeadTask.getTaskDescription());
//    userTask.setDueDate(updatedLeadTask.getDueDate());
//    userTask.setPriority(UserTask.Priority.valueOf(updatedLeadTask.getPriority().name()));
//    userTask.setStage(UserTask.Stage.valueOf(updatedLeadTask.getStatus().name()));
//
//    userTaskRepository.save(userTask);
//
//    return updatedLeadTask;
//  }
//
//  public void deleteLeadTask(String taskId) {
//    leadTaskRepository.deleteById(taskId);
//
//    // Also delete the corresponding UserTask
//    userTaskRepository.deleteById(taskId);
//  }
//}
