package com.ensar.request.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.sql.Date;
import java.sql.Timestamp;

@Schema(description ="Parameters required to create/update a lead task")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateLeadTaskDto {

  @Schema(description = "Lead ID", required = true)
  @NotBlank(message = "Lead ID is required")
  private String leadId;

  @Schema(description =  "Task Name", required = true)
  @NotBlank(message = "Task Name is required")
  @Size(max = 30)
  private String taskName;


  @Schema(description = "Task Description", required = true)
  @NotBlank(message = "Task description is required")
  @Size(max = 500, message = "Task description must be less than 500 characters")
  private String taskDescription;

  @Schema(description ="Assigned To User ID", required = true)
  @NotBlank(message = "Assigned to User ID is required")
  private String assignedToId;

  @Schema(description =" start Date", required = true)
  @NotNull(message = " start date is required")
  private Timestamp startDate;

  @Schema(description ="end Date", required = true)
  @NotNull(message = "end date is required")
  private Timestamp endDate;

  @Schema(description =  "Task Status", required = true)
  @NotBlank(message = "Task status is required")
  private String status;

  @Schema(description = "priority", required = true)
  @NotBlank(message = "priority is required")
  private String priority;

  @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
  private String organizationId;
}
