package com.ensar.service;

import com.ensar.entity.CalendarEvent;
import com.ensar.entity.Lead;
import com.ensar.entity.Organization;
import com.ensar.entity.User;
import com.ensar.repository.CalendarEventRepository;
import com.ensar.repository.LeadRepository;
import com.ensar.repository.UserRepository;
import com.ensar.request.dto.CreateUpdateCalendarEventDto;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.List;

@Service
@Log4j2
@Transactional
public class CalendarEventService {

    private final CalendarEventRepository calendarEventRepository;
    private final UserRepository userRepository;
    private final LeadRepository leadRepository;

    @Autowired
    public CalendarEventService(CalendarEventRepository calendarEventRepository,
                                UserRepository userRepository,
                                LeadRepository leadRepository) {
        this.calendarEventRepository = calendarEventRepository;
        this.userRepository = userRepository;
        this.leadRepository = leadRepository;
    }
    public User getLoggedInUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.debug("Retrieving logged-in user");
        return userRepository.findByEmail(authentication.getName());
    }

    public CalendarEvent getEventById(String id) {
        Optional<CalendarEvent> calendarEventOptional = calendarEventRepository.findById(id);
        if (!calendarEventOptional.isPresent())
            throw new RuntimeException("Calendar event with ID " + id + " not found.");

        return calendarEventOptional.get();
    }

    public List<CalendarEvent> getAllEvents(String orgId) {
        return calendarEventRepository.findByOrganizationId(orgId);
    }

    public CalendarEvent createOrUpdateEvent(Optional<String> eventId, CreateUpdateCalendarEventDto createUpdateCalendarEventDto, Organization organization) {
        CalendarEvent calendarEvent;

        // If eventId is present, find the existing event, otherwise create a new one.
        if (eventId.isPresent()) {
            calendarEvent = calendarEventRepository.findById(eventId.get())
                    .orElseThrow(() -> new RuntimeException("Calendar event with ID " + eventId.get() + " not found"));
        } else {
            calendarEvent = new CalendarEvent();
        }


        User user = userRepository.findById(createUpdateCalendarEventDto.getUser().getId())
                .orElseThrow(() -> new RuntimeException("User with ID " + createUpdateCalendarEventDto.getUser().getId() + " not found"));

        Lead lead = leadRepository.findById(createUpdateCalendarEventDto.getLead().getId())
                .orElseThrow(() -> new RuntimeException("Lead with ID " + createUpdateCalendarEventDto.getLead().getId() + " not found"));

        // Set the User and Lead entities
        calendarEvent.setUser(user);
        calendarEvent.setLead(lead);

        // Set the other fields from the DTO
        calendarEvent.setTitle(createUpdateCalendarEventDto.getTitle());
        calendarEvent.setDescription(createUpdateCalendarEventDto.getDescription());
        calendarEvent.setStartDate(createUpdateCalendarEventDto.getStartDate());
        calendarEvent.setEndDate(createUpdateCalendarEventDto.getEndDate());
        calendarEvent.setAllDay(createUpdateCalendarEventDto.getAllDay());
        calendarEvent.setColor(createUpdateCalendarEventDto.getColor());
        calendarEvent.setAdditionalNote(createUpdateCalendarEventDto.getAdditionalNote());
        calendarEvent.setOrganization(organization);
        // Save the CalendarEvent entity
        return calendarEventRepository.save(calendarEvent);
    }

    public void deleteEvent(String id) {
        calendarEventRepository.deleteById(id);
    }
}
