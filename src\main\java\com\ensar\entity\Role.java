package com.ensar.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Entity(name = "roles")
@Data
@EqualsAndHashCode(callSuper = true)
public class Role extends BaseEntity {

    @Enumerated(EnumType.STRING)
    @Column(name = "role_permission", nullable = false)
    private RolePermission rolePermission;

    @Column(name = "role_description", nullable = false)
    private String roleDescription;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;


    @Column(name = "role_Name", nullable = false)
    private String roleName;

    public enum RolePermission {
        ROLE_SUPER_ADMIN,
        ROLE_ADMIN,
        ROLE_USER
    }
}
