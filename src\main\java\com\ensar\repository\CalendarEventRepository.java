package com.ensar.repository;

import com.ensar.entity.CalendarEvent;
import com.ensar.entity.Project;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CalendarEventRepository extends JpaRepository<CalendarEvent, String> {
    List<CalendarEvent> findByOrganizationId(String organizationId);

}
