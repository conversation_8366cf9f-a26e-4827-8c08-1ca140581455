package com.ensar.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;

import java.sql.Date;

@Entity(name = "lead_responses")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeadResponse extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "reply_id", nullable = false)
    @JsonBackReference
    private LeadReply reply;


    @Column(name = "response", nullable = false)
    private String response;

    @Column(name = "respond_at", nullable = false)
    private Date respondAt;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;
}