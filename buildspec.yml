version: 0.2

env:
  variables:
    AWS_DEFAULT_REGION: "us-east-1"
    AWS_ACCOUNT_ID: "************"
    IMAGE_REPO_NAME: "crm-api"
    IMAGE_TAG: "latest"
    DOCKER_HUB_USERNAME: "chsujith"
    DOCKER_HUB_PASSWORD: "Nellore@123"
    JAVA_HOME: "/usr/lib/jvm/java-17-amazon-corretto"

phases:
  install:
    commands:
      - echo "Downloading and installing Gradle 7.4.2"
      - wget https://services.gradle.org/distributions/gradle-7.4.2-bin.zip
      - unzip gradle-7.4.2-bin.zip
      - export PATH=$PWD/gradle-7.4.2/bin:$PATH
      - echo "Setting execute permissions for gradlew"
      - chmod +x ./gradlew
      - echo "Installing dependencies and setting up Gradle wrapper"
      - ./gradlew wrapper --gradle-version 7.4.2
      

  pre_build:
    commands:
      - echo "Logging in to Docker Hub..."
      - echo "$DOCKER_HUB_PASSWORD" | docker login -u "$DOCKER_HUB_USERNAME" --password-stdin
      - echo "Logging in to Amazon ECR..."
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
      
  build:
    commands:
      - echo "Building the project and Docker image..."
      - ./gradlew build -x test # Build the Java project, skipping tests
      - docker build -t $IMAGE_REPO_NAME:$IMAGE_TAG .
      - docker tag $IMAGE_REPO_NAME:$IMAGE_TAG $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$IMAGE_TAG
      
  post_build:
    commands:
      - echo "Pushing the Docker image..."
      - docker push $AWS_ACCOUNT_ID.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com/$IMAGE_REPO_NAME:$IMAGE_TAG

artifacts:
  files:
    - build/libs/*.jar # Path for the JAR file if using Gradle
    # Uncomment the following line if using Maven
    # - target/*.jar