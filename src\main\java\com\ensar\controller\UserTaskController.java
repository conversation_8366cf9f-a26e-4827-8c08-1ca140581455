package com.ensar.controller;

import com.ensar.entity.Organization;
import com.ensar.entity.UserTask;
import com.ensar.request.dto.CreateUpdateUserTaskDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.UserTaskService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.sql.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Tag(name = "User Task Management")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/usertasks")
public class UserTaskController {

    private final UserTaskService userTaskService;

    @Autowired
    public UserTaskController(UserTaskService userTaskService) {
        this.userTaskService = userTaskService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<UserTask> getUserTaskById(@PathVariable String id) {
        UserTask userTask = userTaskService.getUserTaskById(id);
        return ResponseEntity.ok(userTask);
    }

    @GetMapping("/")
    public ResponseEntity<Map<String, Page<UserTask>>> getUserTasksByPagination(@AuthenticationPrincipal EnsarUserDetails userDetails,
                                                                       @RequestParam(defaultValue = "0") int page,
                                                                       @RequestParam(defaultValue = "10") int size) {
        String orgId = userDetails.getOrganization().getId();
        Page<UserTask> userTaskList = userTaskService.getUserTasksByPagination(orgId, page, size);
        Map<String, Page<UserTask>> response = new HashMap<>();
        response.put("usertasks", userTaskList);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/all")
    public ResponseEntity<Map<String, List<UserTask>>> getAllUserTasks(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<UserTask> userTaskList = userTaskService.getAllUserTasks(orgId);
        Map<String, List<UserTask>> response = new HashMap<>();
        response.put("usertasks", userTaskList);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    public ResponseEntity<UserTask> createUserTask(@Valid @RequestBody CreateUpdateUserTaskDto createUpdateUserTaskDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        UserTask userTask = userTaskService.createOrUpdateUserTask(Optional.empty(), createUpdateUserTaskDto,organization);
        return ResponseEntity.ok(userTask);
    }

    @PutMapping("/{id}")
    public ResponseEntity<UserTask> updateUserTask(@PathVariable String id,
                                                   @Valid @RequestBody CreateUpdateUserTaskDto createUpdateUserTaskDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        UserTask userTask = userTaskService.createOrUpdateUserTask(Optional.of(id), createUpdateUserTaskDto,organization);
        return ResponseEntity.ok(userTask);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUserTask(@PathVariable String id) {
        userTaskService.deleteUserTask(id);
        return ResponseEntity.ok().build();
    }
     @PostMapping("/import")
    public ResponseEntity<Void> importUserTasks(@RequestParam("file") MultipartFile file) throws IOException {
        userTaskService.importUserTasks(file.getInputStream());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportUserTasks() throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        userTaskService.exportUserTasks(outputStream);
        byte[] bytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename("usertasks.csv")
                .build();
        headers.setContentDisposition(contentDisposition);

        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);
    }

}
