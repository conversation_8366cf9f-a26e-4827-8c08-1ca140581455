package com.ensar.controller;

import com.ensar.entity.Designation;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateDesignationDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.DesignationService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/designations")
@Tag(name = "Designation Management") // Class-level Tag annotation
@Slf4j
public class DesignationController {

    private final DesignationService designationService;

    @Autowired
    public DesignationController(DesignationService designationService) {
        this.designationService = designationService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<Designation> getDesignationById(@PathVariable String id) {
        Designation designation = designationService.getDesignationById(id);
        return ResponseEntity.ok(designation);
    }

    @GetMapping("/")
    public ResponseEntity<Map<String, List<Designation>>> getAllDesignations(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<Designation> designationList = designationService.getAllDesignations(orgId);
        Map<String, List<Designation>> response = new HashMap<>();
        response.put("designations", designationList);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    public ResponseEntity<Designation> createDesignation(@Valid @RequestBody CreateUpdateDesignationDto createUpdateDesignationDto,
                                                         @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Designation designation = designationService.createOrUpdateDesignation(Optional.empty(), createUpdateDesignationDto,organization);
        return ResponseEntity.ok(designation);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Designation> updateDesignation(@PathVariable String id,
                                                         @Valid @RequestBody CreateUpdateDesignationDto createUpdateDesignationDto,
                                                         @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Designation designation = designationService.createOrUpdateDesignation(Optional.of(id), createUpdateDesignationDto,organization);
        return ResponseEntity.ok(designation);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDesignation(@PathVariable String id) {
        designationService.deleteDesignation(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/import")
    public ResponseEntity<Void> importDesignations(@RequestParam("file") MultipartFile file) throws IOException {
        designationService.importDesignations(file.getInputStream());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportDesignations() throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        designationService.exportDesignations(outputStream);
        byte[] bytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename("designations.csv")
                .build();
        headers.setContentDisposition(contentDisposition);

        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);
    }
}
