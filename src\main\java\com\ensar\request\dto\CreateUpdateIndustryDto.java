package com.ensar.request.dto;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

@Schema(description = "Parameters required to create/update industry")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateIndustryDto {

    @Schema(description = "ID of the Industry", required = true)
    @NotBlank(message = "ID is required")
    private String id;

    @Schema(description = "Name of the Industry",required = true)
    @NotBlank(message = "Name is required")
    @Size(max = 50, message = "Name cannot exceed 50 characters")
    private String name;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}
