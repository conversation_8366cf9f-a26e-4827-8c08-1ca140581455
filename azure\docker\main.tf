# Configure the Azure provider
terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }
}

provider "azurerm" {
  features {}
}
# Reference an existing resource group
data "azurerm_resource_group" "existing_rg" {
  name = "ai2"
}

# Create an App Service Plan
resource "azurerm_app_service_plan" "asp" {
  name                = "ensar-crm-api-service-plan"
  location            = data.azurerm_resource_group.existing_rg.location
  resource_group_name = data.azurerm_resource_group.existing_rg.name
  sku {
    tier     = "Basic"
    size     = "B3"
  }
  kind = "Linux"
  reserved = true  # Set to true for Linux
}

# Create an App Service
resource "azurerm_app_service" "app" {
  name                = "ensar-crm-h-api"
  location            = data.azurerm_resource_group.existing_rg.location
  resource_group_name = data.azurerm_resource_group.existing_rg.name
  app_service_plan_id = azurerm_app_service_plan.asp.id

  site_config {
    linux_fx_version = "DOCKER|ensar.azurecr.io/crm-api:latest"
  }

  app_settings = {
    WEBSITES_ENABLE_APP_SERVICE_STORAGE = "false"
    DOCKER_REGISTRY_SERVER_URL          = "https://ensar.azurecr.io"
    DOCKER_REGISTRY_SERVER_USERNAME     = "ensar"
    DOCKER_REGISTRY_SERVER_PASSWORD     = "****************************************************"
    "SERVER_PORT"                    = "80"
    "JWT_SECRET_KEY"                 = "fWnJveXKFm0c/tll7gUOwXfXb4j4CbCvuYeYDEGO02M="
    "JWT_TTL_MINS"                   = "180"
    "DB_SERVER"                      = "ensarmysql4.mysql.database.azure.com"
    "DB_PORT"                        = "3306"
    "DB_SCHEMA"                      = "crm"
    "DB_USE_SSL"                     = "true"
    "DB_REQUIRE_SSL"                 = "true"
    "DB_USER"                        = "ensaradmin"
    "DB_PASSWORD"                    = "YourSecurePasswordHere!"

    "EMAIL_FROM"                                 = "<EMAIL>"
    "EMAIL_HOST"                                 = "smtp.gmail.com"
    "EMAIL_USER_NAME"                            = "amzadshaik770"
    "EMAIL_USER_PWD"                             = "cnvnnvdfxihxjvbf"
    "AWS_ACCESS_KEY"                             = "********************"
    "AWS_SECRET_KEY"                             = "8knQ0EbCebTrPttnuYKr/cL7zqkegYQiecw+/j6+"
    "AWS_ACCOUNT_ID"                             = "************"
    "cloud.aws.credentials.accessKey"           = "********************"
    "cloud.aws.credentials.secretKey"           = "8knQ0EbCebTrPttnuYKr/cL7zqkegYQiecw+/j6+"
    "cloud.aws.region.static"                    = "us-east-1"
    "cloud.aws.sqs.queue.url"                    = "https://sqs.us-east-1.amazonaws.com/your-account-id/Crm_Mail.fifo"
    "SENDGRID_API_KEY"                           = "*********************************************************************"
    "spring.servlet.multipart.enabled"           = "true"
    "spring.mail.host"                           = "smtp.gmail.com"
    "spring.mail.port"                           = "587"
    "spring.mail.username"                       = "<EMAIL>"
    "spring.mail.password"                       = "cnvnnvdfxihxjvbf"
    "spring.mail.properties.mail.smtp.starttls.enable"   = "true"
    "spring.mail.properties.mail.smtp.starttls.required" = "true"
    "spring.mail.properties.mail.smtp.auth"      = "true"
  
    "sqs.enabled"                                = "true"
    "mail.smtp.connectiontimeout"                = "10000"
    "mail.smtp.timeout"                          = "10000"
    
    "server.port"                    = "80"
  }
}
