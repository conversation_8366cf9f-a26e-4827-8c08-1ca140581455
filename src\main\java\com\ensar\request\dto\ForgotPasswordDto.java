package com.ensar.request.dto;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

@Schema(description = "Parameters required to request a reset link")
//@Exists.List({
//    @Exists(property = "email", repository = "UserRepository", message = "This email doesn't exists in the db!")
//})
@Accessors(chain = true)
@Setter
@Getter
public class ForgotPasswordDto {
    @Schema(description = "The email address to sent the link to", required = true)
    @Email(message = "Email address is not valid")
    @NotBlank(message = "The email address is required")
    private String email;
}
