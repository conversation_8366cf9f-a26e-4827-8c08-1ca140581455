package com.ensar.controller;

import com.ensar.entity.LeadTask;
import com.ensar.entity.Organization;
import com.ensar.entity.Project;
import com.ensar.request.dto.CreateUpdateLeadTaskDto;
import com.ensar.response.dto.LeadTaskResponseDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.LeadTaskService;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "Lead Tasks Mgmt")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/leadtasks")
@Tag(name = "Lead Tasks management") // Class-level Tag annotation

public class LeadTaskController {

  @Autowired
  private LeadTaskService leadTaskService;

  @GetMapping("/")
//  @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")

//  public ResponseEntity<Map<String,List<LeadTask>>> getAllLeadTasks() {
//    List<LeadTask> leadTasks = leadTaskService.getAllLeadTasks();
//    return ResponseEntity.ok(leadTasks);

//    List<LeadTask> leadTasksList = leadTaskService.getAllLeadTasks();
//    Map<String, List<LeadTask>> response = new HashMap<>();
//    response.put("leadTask", leadTasksList);
//    return ResponseEntity.ok(response);

  public ResponseEntity<List<LeadTaskResponseDto>> getAllLeadTasks(@AuthenticationPrincipal EnsarUserDetails userDetails) {
    String orgId = userDetails.getOrganization().getId();
    List<LeadTaskResponseDto> leadTasks = leadTaskService.getAllLeadTasks(orgId);
    return ResponseEntity.ok(leadTasks);
  }


  @GetMapping("/{id}")
  public ResponseEntity<LeadTaskResponseDto> getLeadTaskById(@PathVariable String id) {
    Optional<LeadTask> leadTask = leadTaskService.getLeadTaskById(id);
    return leadTask.map(task -> ResponseEntity.ok(leadTaskService.convertToDto(task)))
            .orElseGet(() -> ResponseEntity.notFound().build());
  }

  @GetMapping("/lead/{leadId}")
  public ResponseEntity<Map<String, List<LeadTaskResponseDto>>> getLeadTasksByLeadId(@PathVariable String leadId) {
    List<LeadTaskResponseDto> leadTasks = leadTaskService.getLeadTasksByLeadId(leadId);
    Map<String, List<LeadTaskResponseDto>> response = new HashMap<>();
    response.put("leadTasks", leadTasks);
    return ResponseEntity.ok(response);
  }

  @PostMapping("/")
  public ResponseEntity<LeadTaskResponseDto> createLeadTask(@RequestBody @Validated CreateUpdateLeadTaskDto leadTaskDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
    Organization organization = userDetails.getOrganization();
    LeadTask savedLeadTask = leadTaskService.createLeadTask(leadTaskDto,organization);
    return ResponseEntity.ok(leadTaskService.convertToDto(savedLeadTask));
  }

  @PutMapping("/{id}")
  public ResponseEntity<LeadTaskResponseDto> updateLeadTask(@PathVariable String id, @RequestBody @Validated CreateUpdateLeadTaskDto leadTaskDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
    Organization organization = userDetails.getOrganization();
    Optional<LeadTask> existingTask = leadTaskService.getLeadTaskById(id);
    if (!existingTask.isPresent()) {
      return ResponseEntity.notFound().build();
    }
    LeadTask updatedLeadTask = leadTaskService.updateLeadTask(id, leadTaskDto);
    return ResponseEntity.ok(leadTaskService.convertToDto(updatedLeadTask));
  }

  @DeleteMapping("/{id}")
  public ResponseEntity<Void> deleteLeadTask(@PathVariable String id) {
    if (!leadTaskService.getLeadTaskById(id).isPresent()) {
      return ResponseEntity.notFound().build();
    }
    leadTaskService.deleteLeadTask(id);
    return ResponseEntity.ok().build();
  }
}
