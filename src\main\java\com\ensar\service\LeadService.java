
package com.ensar.service;

import com.ensar.entity.*;
import com.ensar.repository.*;
import com.ensar.request.dto.CreateUpdateLeadDto;
import com.ensar.response.dto.*;
import com.ensar.security.EnsarUserDetails;
import com.opencsv.CSVWriter;
import io.micrometer.common.util.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class LeadService {

    @Autowired
    private LeadRepository leadRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private OrganizationRepository organizationRepository;

    @Autowired
    private IndustryRepository industryRepository;

    @Autowired
    private DesignationRepository designationRepository;

    @Autowired
    private RoleRepository roleRepository;

    @Autowired
    private BCryptPasswordEncoder passwordEncoder;

    public Optional<LeadResponseDto> getLeadById(String id) {
        Optional<Lead> lead = leadRepository.findById(id);
        return lead.map(this::convertToDto);
    }

    public List<LeadResponseDto> getAllLeads(String orgId) {
        List<Lead> leads = leadRepository.findByOrganizationId(orgId);
        return leads.stream().map(this::convertToDto).collect(Collectors.toList());
    }

//    public Page<LeadResponseDto> getLeadsByPagination(String orgId, int page, int size) {
//        // Fetch leads from the repository with pagination
//        Pageable pageable = PageRequest.of(page, size);
//        Page<Lead> leads = leadRepository.findByOrganizationId(orgId,pageable);
//
//        // Convert Lead entities to LeadResponseDto
//        List<LeadResponseDto> leadResponseDtos = leads.getContent().stream()
//                .map(this::convertToDto)
//                .collect(Collectors.toList());
//
//        // Return a Page of LeadResponseDto
//        return new PageImpl<>(leadResponseDtos, PageRequest.of(page, size), leads.getTotalElements());
//    }

//    public Page<LeadResponseDto> getLeadsByPagination(EnsarUserDetails loggedInUser, int page, int size) {
//        // Creating Pageable object for pagination
//        Pageable pageable = PageRequest.of(page, size);
//
//        // Create a case-insensitive search pattern
////        String searchPattern = "%" + (search != null ? search : "") + "%";
//
//        // Search query: filter leads by name (case-insensitive), using LIKE query
//        Page<Lead> leads = leadRepository.findByOrganizationId(orgId, pageable);
//
//        // Convert Lead entities to LeadResponseDto
//        List<LeadResponseDto> leadResponseDtos = leads.getContent().stream()
//                .map(this::convertToDto)
//                .collect(Collectors.toList());
//
//        // Return a Page of LeadResponseDto
//        return new PageImpl<>(leadResponseDtos, pageable, leads.getTotalElements());
//    }

public Page<LeadResponseDto> getLeadsByPagination(EnsarUserDetails loggedInUser, int page, int size) {
    Pageable pageable = PageRequest.of(page, size);
    String orgId = loggedInUser.getOrganization().getId();

    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

    boolean isUserRole = loggedInUser.getUser().getRole().getRolePermission().equals(Role.RolePermission.ROLE_USER);

    Page<Lead> leads;

    if (isUserRole) {
        // Return leads where sentBy = loggedInUser.getUser()
        leads = leadRepository.findByOrganizationIdAndSentById(orgId, loggedInUser.getUser().getId(), pageable);
    } else {
        // Return all leads in organization
        leads = leadRepository.findByOrganizationId(orgId, pageable);
    }

    List<LeadResponseDto> leadResponseDtos = leads.getContent().stream()
            .map(this::convertToDto)
            .collect(Collectors.toList());

    return new PageImpl<>(leadResponseDtos, pageable, leads.getTotalElements());
}


    // Fetch all finalized leads (draft_status = false)
//    public List<LeadResponseDto> getAllFinalizedLeads() {
//        List<Lead> leads = leadRepository.findByDraftStatus(false); // Fetch only finalized leads
//        return leads.stream().map(this::convertToDto).collect(Collectors.toList());
//    }

    // Fetch all draft leads (draft_status = true)
    public List<LeadResponseDto> getAllDraftLeads() {
        List<Lead> leads = leadRepository.findByDraftStatus(true); // Fetch only draft leads
        return leads.stream().map(this::convertToDto).collect(Collectors.toList());
    }

    public Lead createOrUpdateLead(Optional<String> id,
                                   CreateUpdateLeadDto createUpdateLeadDto,
                                   Organization org) {
        Lead lead;

        // Check if it's an update or creation
        if (id.isPresent()) {
            lead = leadRepository.findById(id.get()).orElseThrow(() ->
                    new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + id.get()));

            // Uniqueness checks only if the values are non-empty and changed
            if (StringUtils.isNotBlank(createUpdateLeadDto.getEmail()) &&
                    !createUpdateLeadDto.getEmail().equals(lead.getEmail()) &&
                    leadRepository.existsByEmail(createUpdateLeadDto.getEmail())) {
                throw new RuntimeException("Lead with email " + createUpdateLeadDto.getEmail() + " already exists.");
            }

            if (StringUtils.isNotBlank(createUpdateLeadDto.getPhoneNumber()) &&
                    !createUpdateLeadDto.getPhoneNumber().equals(lead.getPhoneNumber()) &&
                    leadRepository.existsByPhoneNumber(createUpdateLeadDto.getPhoneNumber())) {
                throw new RuntimeException("Lead with phonenumber " + createUpdateLeadDto.getPhoneNumber() + " already exists.");
            }

            if (StringUtils.isNotBlank(createUpdateLeadDto.getLinkedin()) &&
                    !createUpdateLeadDto.getLinkedin().equals(lead.getLinkedin()) &&
                    leadRepository.existsByLinkedin(createUpdateLeadDto.getLinkedin())) {
                throw new RuntimeException("Lead with LinkedIn " + createUpdateLeadDto.getLinkedin() + " already exists.");
            }

        } else {
            // Uniqueness checks only for new leads with non-empty values
            if (StringUtils.isNotBlank(createUpdateLeadDto.getEmail()) && leadRepository.existsByEmail(createUpdateLeadDto.getEmail())) {
                throw new RuntimeException("Lead with email " + createUpdateLeadDto.getEmail() + " already exists.");
            }
            if (StringUtils.isNotBlank(createUpdateLeadDto.getPhoneNumber()) && leadRepository.existsByPhoneNumber(createUpdateLeadDto.getPhoneNumber())) {
                throw new RuntimeException("Lead with phonenumber " + createUpdateLeadDto.getPhoneNumber() + " already exists.");
            }
            if (StringUtils.isNotBlank(createUpdateLeadDto.getLinkedin()) && leadRepository.existsByLinkedin(createUpdateLeadDto.getLinkedin())) {
                throw new RuntimeException("Lead with LinkedIn " + createUpdateLeadDto.getLinkedin() + " already exists.");
            }
            lead = new Lead();
        }

        // Set lead details
        lead.setFirstname(createUpdateLeadDto.getFirstname());
        lead.setLastname(createUpdateLeadDto.getLastname());

        Industry industry = industryRepository.findById(createUpdateLeadDto.getIndustryId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                        "Industry not found with id " + createUpdateLeadDto.getIndustryId()));
        lead.setIndustry(industry);

        Designation designation = designationRepository.findById(createUpdateLeadDto.getDesignationId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                        "Designation not found with id " + createUpdateLeadDto.getDesignationId()));
        lead.setDesignation(designation);

        // Set nullable and unique values
        lead.setEmail(StringUtils.isBlank(createUpdateLeadDto.getEmail()) ? null : createUpdateLeadDto.getEmail());
        lead.setPhoneNumber(StringUtils.isBlank(createUpdateLeadDto.getPhoneNumber()) ? null : createUpdateLeadDto.getPhoneNumber());
        lead.setLinkedin(StringUtils.isBlank(createUpdateLeadDto.getLinkedin()) ? null : createUpdateLeadDto.getLinkedin());

        lead.setStatus(createUpdateLeadDto.getStatus());
        lead.setLeaddate(createUpdateLeadDto.getLeaddate());
        lead.setWebsite(createUpdateLeadDto.getWebsite());
        lead.setRegion(createUpdateLeadDto.getRegion());
        lead.setEmpCount(createUpdateLeadDto.getEmpCount());
        lead.setVerified(createUpdateLeadDto.getVerified());
        lead.setMessageSent(createUpdateLeadDto.getMessageSent());
        lead.setComments(createUpdateLeadDto.getComments());
        lead.setOrganization(org);

        if (createUpdateLeadDto.getDraftStatus() != null) {
            lead.setDraftStatus(createUpdateLeadDto.getDraftStatus());
        } else {
            lead.setDraftStatus(false);  // Default to false (not a draft)
        }

        if (createUpdateLeadDto.getSentById() != null) {
            User user = userRepository.findById(createUpdateLeadDto.getSentById())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                            "User not found with id " + createUpdateLeadDto.getSentById()));
            lead.setSentBy(user);
        }
//        if (org.getId() != null) {
//            Organization organization = organizationRepository.findById(createUpdateLeadDto.getOrganizationId())
//                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
//                            "Organization not found with id " + createUpdateLeadDto.getOrganizationId()));
//            lead.setOrganization(organization);
//        }

        return leadRepository.save(lead);
    }


    public void deleteLead(String id) {
        Lead lead = leadRepository.findById(id)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + id));
        leadRepository.delete(lead);
    }
    public void deleteDraftLead(String id) {
        Lead lead = leadRepository.findById(id).orElseThrow(() ->
                new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + id));

        // Only delete if it's a draft
        if (lead.getDraftStatus()) {
            leadRepository.delete(lead);
        } else {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Lead is not a draft and cannot be deleted.");
        }
    }



    public void exportLeads(HttpServletResponse response, EnsarUserDetails loggedInUser) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String loggedInUserEmail = authentication.getName();
        response.setContentType("text/csv");
        response.setHeader("Content-Disposition", "attachment; filename=leads.csv");

        try (CSVWriter writer = new CSVWriter(response.getWriter())) {
            String[] header = { "ID", "FirstName","LastName ", "Industry", "designation", "Email", "Phone Number", "Status", "Lead Date",
                    "LinkedIn", "Website", "Region", "Employee Count", "Verified", "Message Sent",
                    "Comments", "Sent By" };
            writer.writeNext(header);

            List<Lead> leads;

            if(loggedInUser.getUser().getRole().getRolePermission().equals(Role.RolePermission.ROLE_SUPER_ADMIN)){
                leads = leadRepository.findAll();
            }else {
                leads = leadRepository.findAll().stream()
                        .filter(lead -> lead.getSentBy() != null && lead.getSentBy().getEmail().equals(loggedInUserEmail))
                        .collect(Collectors.toList());
            }




            for (Lead lead : leads) {
                String empCount = lead.getEmpCount() != null ? lead.getEmpCount().replace("-", " to ") : "";
                String[] data = {
                        lead.getId(),
                        lead.getFirstname(),
                        lead.getLastname(),
                        lead.getIndustry() != null ? lead.getIndustry().getName() : "",
                        lead.getDesignation() != null ? lead.getDesignation().getName() : "",
                        lead.getEmail(),
                        lead.getPhoneNumber(),
                        lead.getStatus().name(),
                        lead.getLeaddate() != null ? lead.getLeaddate().toString() : "",
                        lead.getLinkedin(),
                        lead.getWebsite(),
                        lead.getRegion(),
                        empCount,
                        lead.getVerified().toString(),
                        lead.getMessageSent().toString(),
                        lead.getComments(),
                        lead.getSentBy() != null ? lead.getSentBy().getEmail() : ""
                };
                writer.writeNext(data);
            }
        } catch (IOException e) {
            throw new RuntimeException("Error occurred while exporting leads", e);
        }
    }
//    public void importLeads(MultipartFile file,Organization org) {
//        try (CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream()))) {
//            List<String[]> data = reader.readAll();
//
//            // Skip the header row
//            if (!data.isEmpty()) {
//                data.remove(0);
//            }
//
//            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd"); // Adjust date format if necessary
//
//            for (String[] row : data) {
//                if (row.length < 15) continue; // Skip incomplete rows
//
//                CreateUpdateLeadDto dto = new CreateUpdateLeadDto();
//                dto.setFirstname(row[1]);
//                dto.setLastname(row[2]);
//
//                // Fetch and set the Industry
//                Industry industry = industryRepository.findByName(row[3])
//                        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Industry not found with name " + row[3]));
//                dto.setIndustryId(industry.getId());
//                dto.setEmail(row[5]);
//                dto.setPhonenumber(row[6]);
//                // dto.setStatus(row[6]);
//                dto.setLinkedin(row[7]);
//                dto.setWebsite(row[8]);
//                dto.setRegion(row[9]);
//                dto.setEmpcount();(row[10]);
//                dto.setVerified(Boolean.parseBoolean(row[11]));
//                dto.setMessagesent(Boolean.parseBoolean(row[12]));
//                dto.setComments(row[13]);
//
//                try {
//                    if (!row[14].isEmpty()) {
//                        Date date = (Date) dateFormat.parse(row[14]);
//                        dto.setLeaddate(new Date(date.getTime()));
//                    }
//                } catch (ParseException e) {
//                    throw new RuntimeException("Error parsing date in CSV", e);
//                }
//
//                if (row.length > 15 && !row[15].isEmpty()) {
//                    User user = userRepository.findByEmail(row[15]);
//                    dto.setSentbyId(user.getId());
//                }
//
//                createOrUpdateLead(Optional.empty(), dto,org);
//            }
//        } catch (IOException | CsvException e) {
//            throw new RuntimeException("Error occurred while importing leads", e);
//        }
//    }

    public static String removeEmojis(String input) {
        if (input == null) return null;
        return input.replaceAll("[\\x{1F600}-\\x{1F64F}" +  // emoticons
                "\\x{1F300}-\\x{1F5FF}" +  // symbols & pictographs
                "\\x{1F680}-\\x{1F6FF}" +  // transport & map symbols
                "\\x{1F700}-\\x{1F77F}" +  // alchemical symbols
                "\\x{1F780}-\\x{1F7FF}" +  // geometric shapes extended
                "\\x{1F800}-\\x{1F8FF}" +  // supplemental arrows
                "\\x{1F900}-\\x{1F9FF}" +  // supplemental symbols and pictographs
                "\\x{1FA00}-\\x{1FA6F}" +  // chess symbols
                "\\x{1FA70}-\\x{1FAFF}" +  // symbols and pictographs extended-A
                "\\x{2600}-\\x{26FF}" +    // miscellaneous symbols
                "\\x{2700}-\\x{27BF}" +    // dingbats
                "\\x{1F004}-\\x{1F0CF}]", "");
    }

    private final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

    private String getStringValue(Row row, Integer colIndex) {
        if (colIndex == null) return "";
        Cell cell = row.getCell(colIndex);
        if (cell == null) return "";

        if (cell.getCellType() == CellType.STRING) {
            return cell.getStringCellValue().trim();
        } else if (cell.getCellType() == CellType.NUMERIC) {
            if (DateUtil.isCellDateFormatted(cell)) {
                return dateFormat.format(cell.getDateCellValue());
            } else {
                return String.valueOf((long) cell.getNumericCellValue());
            }
        }
        return cell.toString().trim();
    }

    public Map<String, Object> importLeads(MultipartFile file, EnsarUserDetails loggedInUser) {
        try {
            Workbook workbook = new XSSFWorkbook(file.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);

            Row headerRow = sheet.getRow(0);
            Map<String, Integer> headerMap = new HashMap<>();
            for (Cell cell : headerRow) {
                headerMap.put(cell.getStringCellValue().trim(), cell.getColumnIndex());
            }

            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

            Industry industry = industryRepository.findByName("IT").orElse(null);

            Role roleUser = roleRepository.findByRoleName("user");

            Map<String, Object> response = new HashMap<>();
            int duplicateCount = 0;
            int errorCount = 0;

            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                try {
                    Row row = sheet.getRow(rowIndex);
                    if (row == null) continue;

                    String rawName = getStringValue(row, headerMap.get("Name"));
                    String cleanName = removeEmojis(rawName).trim();

                    String firstName = "";
                    String lastName = "";

                    if (!cleanName.isEmpty()) {
                        List<String> nameParts = new ArrayList<>();
                        Matcher m = Pattern.compile("\\S+|\\([^\\)]+\\)").matcher(cleanName);
                        while (m.find()) {
                            nameParts.add(m.group());
                        }
                        firstName = nameParts.get(0);
                        lastName = nameParts.size() > 1 ? String.join(" ", nameParts.subList(1, nameParts.size())) : "";
                        if (lastName.length() > 50) {
                            List<String> words = new ArrayList<>(Arrays.asList(lastName.split("\\s+")));
                            while (String.join(" ", words).length() > 50 && !words.isEmpty()) {
                                words.remove(words.size() - 1);
                            }
                            lastName = String.join(" ", words);
                        }
                    }

                    String email = getStringValue(row, headerMap.get("Mail ID"));
                    String phone = getStringValue(row, headerMap.get("Mobile Number"));
                    String linkedin = removeEmojis(getStringValue(row, headerMap.get("Lead link")));
                    String website = removeEmojis(getStringValue(row, headerMap.get("Website")));
                    String region = getStringValue(row, headerMap.get("State"));
                    String empCount = getStringValue(row, headerMap.get("Company headcount")).replace(" to ", "-").trim();
                    String occupation = getStringValue(row, headerMap.get("Occupation"));
                    String sentByRaw = getStringValue(row, headerMap.get("Sent By"));
                    boolean messageSent = "Done".equalsIgnoreCase(getStringValue(row, headerMap.get("sent")).trim());

                    // Parse lead date
                    String leadDateStr = getStringValue(row, headerMap.get("Sent Date"));
                    java.sql.Date leadDate = null;
                    if (leadDateStr != null && !leadDateStr.isEmpty()) {
                        java.util.Date utilDate = dateFormat.parse(leadDateStr);
                        leadDate = new java.sql.Date(utilDate.getTime());
                    }

                    // Duplicate check: Check if first+last name OR linkedin exists
                    boolean isDuplicateByName = false;
                    boolean isDuplicateByLinkedin = false;
                    boolean isDuplicateByEmail = false;

                    if (!firstName.isEmpty() && !lastName.isEmpty()) {
                        isDuplicateByName = leadRepository.existsByFirstnameAndLastname(firstName.trim(), lastName.trim());
                    }

                    if (linkedin != null && !linkedin.trim().isEmpty()) {
                        isDuplicateByLinkedin = leadRepository.existsByLinkedin(linkedin.trim());
                    }

                    if (email != null && !email.trim().isEmpty()) {
                        isDuplicateByEmail = leadRepository.existsByEmail(email.trim());
                    }


                    if (isDuplicateByName || isDuplicateByLinkedin || isDuplicateByEmail) {
                        duplicateCount++;
                        System.out.println("Duplicate found at row " + (rowIndex + 1) +
                                " with Name: " + firstName + " " + lastName + ", or LinkedIn: " + linkedin);
                        continue;  // Skip this record
                    }

                    // Get or create designation
                    Designation designation = designationRepository.findByName(occupation);
                    if (designation == null) {
                        if (occupation != null && !occupation.trim().isEmpty()) {
                            Designation d = new Designation();
                            d.setName(occupation);
                            d.setOrganization(loggedInUser.getOrganization());
                            designation = designationRepository.save(d);
                        } else {
                            designation = designationRepository.findByName("Business Analyst");
                        }
                    }

                    // Handle SentBy user
                    User sentBy;
                    if (sentByRaw != null && !sentByRaw.trim().isEmpty()) {
                        String[] parts = sentByRaw.trim().split("\\s+");
                        String fname = parts[0];
                        String lname = parts.length > 1 ? parts[1] : "User";
                        String mailGen = fname.toLowerCase() + lname.toLowerCase() + "@ensarsolutions.com";

                        sentBy = userRepository.findByEmail(mailGen);
                        if (sentBy == null) {
                            User u = new User();
                            u.setFirstName(fname);
                            u.setLastName(lname);
                            u.setPassword(passwordEncoder.encode("Demo@123"));
                            u.setEmail(mailGen);
                            u.setRole(roleUser);
                            u.setOrganization(loggedInUser.getOrganization());
                            sentBy = userRepository.save(u);
                        }
                    } else {
                        sentBy = userRepository.findByEmail(loggedInUser.getUser().getEmail());
                    }

                    // Create and save lead
                    Lead lead = new Lead();
                    lead.setFirstname(firstName);
                    lead.setLastname(lastName);
                    lead.setEmail(email);
                    lead.setOrganization(loggedInUser.getOrganization());
                    lead.setDesignation(designation);
                    lead.setPhoneNumber(phone);
                    lead.setLinkedin(linkedin);
                    lead.setWebsite(website);
                    lead.setStatus(Lead.LeadStatus.New);
                    lead.setRegion(region);
                    lead.setEmpCount(empCount);
                    lead.setMessageSent(messageSent);
                    lead.setSentBy(sentBy);
                    lead.setLeaddate(leadDate);
                    lead.setIndustry(industry);
                    lead.setComments("");  // blank as per python code

                    leadRepository.save(lead);

                } catch (Exception e) {
                    errorCount++;
                    System.err.println("Error at row " + (rowIndex + 1) + ": " + e.getMessage());
                }
            }

            System.out.println("Duplicate records skipped: " + duplicateCount);
            System.out.println("Records inserted successfully: " + (sheet.getLastRowNum() - duplicateCount - errorCount));

            response.put("duplicates", duplicateCount);
            response.put("inserted", sheet.getLastRowNum() - duplicateCount - errorCount);

            return response;

        } catch (IOException e) {
            throw new RuntimeException("Error processing file or parsing date", e);
        }
    }



    public List<LeadResponseDto> getLeadsSentBy(String email) {
        List<Lead> leads = leadRepository.findBySentByEmail(email);
        return leads.stream().map(this::convertToDto).collect(Collectors.toList());
    }




    public LeadResponseDto convertToDto(Lead lead) {
        LeadResponseDto dto = new LeadResponseDto();
        dto.setId(lead.getId());
        dto.setFirstname(lead.getFirstname());
        dto.setLastname(lead.getLastname());

        Industry industry = lead.getIndustry();
        if (industry != null) {
            IndustryResponseDto industryDto = new IndustryResponseDto();
            industryDto.setId(industry.getId());
            industryDto.setName(industry.getName());
            dto.setIndustry(industryDto);
        }

        Designation designation = lead.getDesignation();
        if (designation != null) {
            DesignationResponseDto designationDto = new DesignationResponseDto();
            designationDto.setId(designation.getId());
            designationDto.setName(designation.getName());
            dto.setDesignation(designationDto);
        }

        dto.setEmail(lead.getEmail());
        dto.setPhoneNumber(lead.getPhoneNumber());
        dto.setStatus(lead.getStatus().name());
        dto.setLeaddate(lead.getLeaddate());
        dto.setLinkedin(lead.getLinkedin());
        dto.setWebsite(lead.getWebsite());
        dto.setRegion(lead.getRegion());
        dto.setEmpCount(lead.getEmpCount());
        dto.setVerified(lead.getVerified());
        dto.setMessageSent(lead.getMessageSent());
        dto.setComments(lead.getComments());
        dto.setDraftStatus(lead.getDraftStatus());

        if (lead.getSentBy() != null) {
            UserResponseDto userDto = new UserResponseDto();
            userDto.setId(lead.getSentBy().getId());
            userDto.setFirstName(lead.getSentBy().getFirstName());
            userDto.setLastName(lead.getSentBy().getLastName());
            userDto.setEmail(lead.getSentBy().getEmail());
            dto.setSentBy(userDto);
        }

        if (lead.getOrganization() != null) {
            OrganizationResponseDto organizationDto = new OrganizationResponseDto();
            organizationDto.setId(lead.getOrganization().getId());
            organizationDto.setName(lead.getOrganization().getName());
            organizationDto.setDomain(lead.getOrganization().getDomain());
            dto.setOrganization(organizationDto);
        }



        List<LeadReplyResponseDto> leadReplyDtos = lead.getReplies() != null ?
                lead.getReplies().stream().map(this::convertToDto).collect(Collectors.toList()) : new ArrayList<>();
        dto.setLeadReplies(leadReplyDtos);

        return dto;
    }

    private LeadReplyResponseDto convertToDto(LeadReply reply) {
        LeadReplyResponseDto dto = new LeadReplyResponseDto();
        dto.setId(reply.getId());
        dto.setLeadId(reply.getLead().getId());
        dto.setReplyText(reply.getReplyText());
        dto.setReplyAt(reply.getReplyAt());

        if (reply.getReplier() != null) {
            UserResponseDto userDto = new UserResponseDto();
            userDto.setId(reply.getReplier().getId());
            userDto.setFirstName(reply.getReplier().getFirstName());
            userDto.setLastName(reply.getReplier().getLastName());
            userDto.setEmail(reply.getReplier().getEmail());
            dto.setReplier(userDto);
        }

        List<LeadResponseResponseDto> leadResponseDtos = reply.getResponses() != null ?
                reply.getResponses().stream().map(this::convertToDto).collect(Collectors.toList()) : new ArrayList<>();
        dto.setLeadresponses(leadResponseDtos);

        return dto;
    }

    private LeadResponseResponseDto convertToDto(LeadResponse response) {
        LeadResponseResponseDto dto = new LeadResponseResponseDto();
        dto.setId(response.getId());
        dto.setReplyId(response.getReply().getId());
        dto.setResponse(response.getResponse());
        dto.setRespondAt(response.getRespondAt().toString());

        return dto;
    }

}
