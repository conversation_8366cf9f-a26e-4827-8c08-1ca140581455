DROP TABLE IF EXISTS cms_contents;

CREATE TABLE cms_contents (
    id                      CHAR(36) PRIMARY KEY,
    title                   VARCHAR(255) NOT NULL,
    description             VARCHAR(500),
    content                 TEXT NOT NULL,
    meta_title              VARCHAR(25) NOT NULL,
    meta_tags               VARCHAR(25) NOT NULL,
    meta_description        VARCHAR(500),
    meta_keywords           VARCHAR(25) NOT NULL,
    cover_url               VARCHAR(255),
    cover_url_data          LONGBLOB,
    organization_id         CHAR(36) NOT NULL,
    created_date_time       TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time  TIMESTAMP NULL DEFAULT NULL,
    CONSTRAINT fk_cms_content_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8;


DROP TABLE IF EXISTS cms_mails;
CREATE TABLE cms_mails (
    id                      CHAR(36) PRIMARY KEY,
    subject                   VARCHAR(255) NOT NULL,
    content                 TEXT NOT NULL,
    organization_id         CHAR(36) NOT NULL,
    created_date_time       TIMES<PERSON>MP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time  TIMESTAMP NULL DEFAULT NULL,
    CONSTRAINT fk_cms_mail_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8;
