package com.ensar.repository;

import com.ensar.entity.Designation;
import com.ensar.entity.Project;
import com.ensar.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface DesignationRepository extends JpaRepository<Designation, String> {

    Designation findByName(String name);

    boolean existsByName(String name);

    List<Designation> findByDescriptionContaining(String keyword);

    List<Designation> findByOrganizationId(String organizationId);

    Designation findByNameIgnoreCase(String name);
}
