package com.ensar.service;

import com.ensar.entity.Company;
import com.ensar.entity.Industry;
import com.ensar.entity.Organization;
import com.ensar.entity.Project;
import com.ensar.mapper.CompanyMapper;
import com.ensar.repository.CompanyRepository;
import com.ensar.repository.IndustryRepository;
import com.ensar.request.dto.CreateUpdateCompanyDto;
import com.ensar.response.dto.CompanyResponseDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class CompanyService {

    @Autowired
    private CompanyRepository companyRepository;

    @Autowired
    private IndustryRepository industryRepository;

//    public List<CompanyResponseDto> getAllCompanies() {
//        return companyRepository.findAll().stream()
//            .map(CompanyMapper::toDto)
//            .collect(Collectors.toList());
//    }

    public List<CompanyResponseDto> getAllCompanies(String orgId) {
        return companyRepository.findByOrganizationId(orgId);
    }


    public CompanyResponseDto getCompanyById(String id) {
        Company company = companyRepository.findById(id)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found"));
        return CompanyMapper.toDto(company);
    }

    public CompanyResponseDto saveCompany(CreateUpdateCompanyDto dto, Organization organization) {
        Industry industry = industryRepository.findById(dto.getIndustryId())
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Industry not found"));

        Company company = CompanyMapper.toEntity(dto, industry);
        company.setOrganization(organization);
        company = companyRepository.save(company);

        return CompanyMapper.toDto(company);
    }

    public void deleteCompany(String id) {
        if (!companyRepository.existsById(id)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Company not found");
        }
        companyRepository.deleteById(id);
    }
}
