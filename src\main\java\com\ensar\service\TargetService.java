package com.ensar.service;

import com.ensar.entity.Organization;
import com.ensar.entity.Role;
import com.ensar.entity.Target;
import com.ensar.entity.User;
import com.ensar.repository.TargetRepository;
import com.ensar.repository.UserRepository;
import com.ensar.request.dto.CreateUpdateTargetDto;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.io.*;
import java.sql.Date;
import java.util.*;

@Service
@Log4j2
@Transactional
public class TargetService {

    @Autowired
    private final UserRepository userRepository;

    private final TargetRepository targetRepository;

    @Autowired
    public TargetService(TargetRepository targetRepository, UserRepository userRepository) {
        this.targetRepository = targetRepository;
        this.userRepository = userRepository;

    }

    public Target getTargetById(String id) {
        Optional<Target> targetOptional = targetRepository.findById(id);
        if (!targetOptional.isPresent())
            throw new RuntimeException("Target with ID " + id + " not found.");

        return targetOptional.get();
    }
    public User getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    public User getLoggedInUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return getUserByEmail(authentication.getName());
    }

    public Page<Target> getTargetsByPagination(String orgId, int page, int size) {
        User user = getLoggedInUser();
        Pageable pageable = PageRequest.of(page, size);

        return targetRepository.findByOrganizationId(orgId,pageable);
//        switch (user.getRole().getRolePermission()) {
//            case ROLE_SUPER_ADMIN:
//            case ROLE_ADMIN:
//                return targetRepository.findByOrganizationId(orgId, pageable);
//            case ROLE_USER:
//                return targetRepository.findByHandledBy(user, pageable);
//            default:
//                return Page.empty();
//        }
    }

    public List<Target> getAllTargets(String orgId) {
        User user = getLoggedInUser();
        return targetRepository.findByOrganizationId(orgId);
//        switch (user.getRole().getRolePermission()) {
//            case ROLE_SUPER_ADMIN:
//            case ROLE_ADMIN:
//                return targetRepository.findByOrganizationId(orgId);
//            case ROLE_USER:
//                return targetRepository.findByHandledBy(user);
//            default:
//                return List.of();
//        }
    }

//    public List<Target> getAllTargets(String orgId) {
//
//        User user = getLoggedInUser();
//
//        if (user.getRole().getRolePermission().equals(Role.RolePermission.ROLE_SUPER_ADMIN)) {
//            return targetRepository.findByOrganizationId(orgId); // Super admin gets all targets
//        }
//
//        if (user.getRole().getRolePermission().equals(Role.RolePermission.ROLE_ADMIN)) {
//            return targetRepository.findByOrganizationId(orgId); // Admin gets all targets
//        }
//
//        if (user.getRole().getRolePermission().equals(Role.RolePermission.ROLE_USER)) {
//            // Return only targets assigned to the current user
//            return targetRepository.findByHandledBy(user);
//        }
//
//        return List.of(); // Return empty list if no match
//    }

//        User user = getLoggedInUser();
//        Role.RoleName roleName = user.getRole().getRoleName();
//        if (Role.RoleName.ROLE_SUPER_ADMIN.equals(roleName)) {
//        return targetRepository.findAll();
//    }else if (Role.RoleName.ROLE_ADMIN.equals(roleName)) {
//            // Assuming that an ADMIN user can only see organizations they are associated with
//            return targetRepository.findAll();
//        } else if (Role.RoleName.ROLE_USER.equals(roleName)) {
//            // Assuming that a regular USER cannot view organizations
//            return targetRepository.findByHandledBy(user);
//        }
//        // Return an empty list for roles that are not handled
//        return List.of();
//    }



    public Target createOrUpdateTarget(Optional<String> targetId,
                                       CreateUpdateTargetDto createUpdateTargetDto,
                                       Organization organization) {
        Target target;
        if (targetId.isPresent()) {
            target = targetRepository.findById(targetId.get())
                    .orElseThrow(() -> new RuntimeException("Target with ID " + targetId.get() + " not found"));
        } else {
            target = new Target();
        }
        String responseReceived = Objects.equals(createUpdateTargetDto.getResponseReceived(), "true") ? "YES" : "NO";
        // Set fields from DTO
        target.setAccountName(createUpdateTargetDto.getAccountName());
        target.setConnectionsCount(createUpdateTargetDto.getConnectionsCount());
//        target.setHandledBy(createUpdateTargetDto.getHandledBy());
        target.setNoOfLeadsIdentified(createUpdateTargetDto.getNoOfLeadsIdentified());
        target.setConnectionsSent(createUpdateTargetDto.getConnectionsSent());
        target.setMessagesSent(createUpdateTargetDto.getMessagesSent());
        target.setFollowUps(createUpdateTargetDto.getFollowUps());
        target.setResponseReceived(responseReceived);
        target.setMeetingsScheduled(createUpdateTargetDto.getMeetingsScheduled());
        target.setInMailCount(createUpdateTargetDto.getInMailCount());
        target.setPostings(createUpdateTargetDto.getPostings());
        target.setCreatedDate(createUpdateTargetDto.getCreatedDate());
        target.setOrganization(organization);
        target.setStatus(createUpdateTargetDto.getStatus());


        if (createUpdateTargetDto.getHandledById() != null) {
            User user = userRepository.findById(createUpdateTargetDto.getHandledById())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                            "User not found with id " + createUpdateTargetDto.getHandledById()));
            target.setHandledBy(user);
        }


        return targetRepository.save(target);
    }

    public void deleteTarget(String id) {
        targetRepository.deleteById(id);
    }

    public void importTargets(InputStream inputStream) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        List<Target> targets = new ArrayList<>();
        Map<String, Integer> headerMap = new HashMap<>();

        if ((line = reader.readLine()) != null) {
            String[] headers = line.split(",");
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
        } else {
            throw new IOException("Empty CSV file");
        }

        while ((line = reader.readLine()) != null) {
            String[] fields = line.split(",");

            if (fields.length != headerMap.size()) {
                throw new IOException("Invalid CSV format. Each line must have the same number of fields as the header.");
            }

            Target target = new Target();
            try {
                String accountName = fields[headerMap.get("account_name")];
                Integer connectionsCount = Integer.valueOf(fields[headerMap.get("connections_count")]);
                String handledBy = fields[headerMap.get("handled_by")];
                Integer noOfLeadsIdentified = Integer.valueOf(fields[headerMap.get("no_of_leads_identified")]);
                Integer connectionsSent = Integer.valueOf(fields[headerMap.get("connections_sent")]);
                Integer messagesSent = Integer.valueOf(fields[headerMap.get("messages_sent")]);
                Integer followUps = Integer.valueOf(fields[headerMap.get("follow_ups")]);
                String responseReceived = String.valueOf(fields[headerMap.get("response_received")]);
                Integer meetingsScheduled = Integer.valueOf(fields[headerMap.get("meetings_scheduled")]);
                Integer inMailCount = Integer.valueOf(fields[headerMap.get("in_mail_count")]);
                Integer postings = Integer.valueOf(fields[headerMap.get("postings")]);
                Date createdDate = Date.valueOf(fields[headerMap.get("createdDate")]);


                target.setAccountName(accountName);
                target.setConnectionsCount(connectionsCount);
//                target.setHandledBy(handledBy);
                target.setNoOfLeadsIdentified(noOfLeadsIdentified);
                target.setConnectionsSent(connectionsSent);
                target.setMessagesSent(messagesSent);
                target.setFollowUps(followUps);
                target.setResponseReceived(responseReceived);
                target.setMeetingsScheduled(meetingsScheduled);
                target.setInMailCount(inMailCount);
                target.setPostings(postings);
                target.setCreatedDate(createdDate);
            } catch (Exception e) {
                throw new IOException("Error processing line: " + line, e);
            }

            targets.add(target);
        }
        targetRepository.saveAll(targets);
    }

    public void exportTargets(OutputStream outputStream) throws IOException {
        List<Target> targets = targetRepository.findAll();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("Account Name,Connections Count,Handled By,No of Leads Identified,Connections Sent,Messages Sent,Follow Ups,Response Received,Meetings Scheduled,In Mail Count,Postings,Created Date\n");
        for (Target target : targets) {
            writer.write(String.format("%s,%d,%s,%d,%d,%d,%d,%b,%s,%d,%d,%d\n",
                    target.getAccountName(),
                    target.getConnectionsCount(),
                    target.getHandledBy(),
                    target.getNoOfLeadsIdentified(),
                    target.getConnectionsSent(),
                    target.getMessagesSent(),
                    target.getFollowUps(),
                    target.getResponseReceived(),
                    target.getMeetingsScheduled(),
                    target.getInMailCount(),
                    target.getPostings()));
                    target.getCreatedDate();
        }
        writer.flush();
    }
}
