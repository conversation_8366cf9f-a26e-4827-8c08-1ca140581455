package com.ensar.controller;

import com.ensar.entity.LeadResponse;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateLeadResponseDto;
import com.ensar.response.dto.LeadResponseDto;
import com.ensar.response.dto.LeadResponseResponseDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.LeadResponseService;

import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "Lead Responses Mgmt")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/leadresponses")
@Tag(name = "Lead Responses management") // Class-level Tag annotation

public class LeadResponseController {

    @Autowired
    private LeadResponseService leadResponseService;

    @GetMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<List<LeadResponseResponseDto>> getAllLeadResponses(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<LeadResponseResponseDto> leadResponses = leadResponseService.getAllLeadResponses(orgId);
        return ResponseEntity.ok(leadResponses);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<LeadResponseResponseDto> getLeadResponseById(@PathVariable String id) {
        Optional<LeadResponse> leadResponse = leadResponseService.getLeadResponseById(id);
        return leadResponse.map(response -> ResponseEntity.ok(leadResponseService.convertToDto(response)))
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    @GetMapping("/reply/{replyId}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Map<String, List<LeadResponseResponseDto>>> getLeadResponsesByReplyId(@PathVariable String replyId) {
        List<LeadResponseResponseDto> leadResponses = leadResponseService.getResponsesForReply(replyId);
        Map<String, List<LeadResponseResponseDto>> response = new HashMap<>();
        response.put("leadResponses", leadResponses);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<LeadResponseResponseDto> createLeadResponse(@RequestBody CreateUpdateLeadResponseDto leadResponseDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        LeadResponse savedLeadResponse = leadResponseService.createLeadResponse(leadResponseDto,organization);
        return ResponseEntity.ok(leadResponseService.convertToDto(savedLeadResponse));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<LeadResponseResponseDto> updateLeadResponse(@PathVariable String id, @RequestBody CreateUpdateLeadResponseDto leadResponseDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        if (!leadResponseService.getLeadResponseById(id).isPresent()) {
            return ResponseEntity.notFound().build();
        }
        LeadResponse updatedLeadResponse = leadResponseService.updateLeadResponse(id, leadResponseDto);
        return ResponseEntity.ok(leadResponseService.convertToDto(updatedLeadResponse));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Void> deleteLeadResponse(@PathVariable String id) {
        leadResponseService.deleteLeadResponse(id);
        return ResponseEntity.ok().build();
    }
}
