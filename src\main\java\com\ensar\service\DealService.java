package com.ensar.service;

import com.ensar.entity.Deal;
import com.ensar.entity.Lead;
import com.ensar.entity.Organization;
import com.ensar.repository.DealRepository;
import com.ensar.repository.LeadRepository;
import com.ensar.request.dto.CreateUpdateDealDto;
import com.ensar.request.dto.DealStatsDto;
import jakarta.transaction.Transactional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Service
@Slf4j
@Transactional
public class DealService {

    private final DealRepository dealRepository;
    private final LeadRepository leadRepository;

    @Autowired
    public DealService(DealRepository dealRepository, LeadRepository leadRepository) {
        this.dealRepository = dealRepository;
        this.leadRepository = leadRepository;
    }

    public Deal getDealById(String id) {
        return dealRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Deal with ID " + id + " not found."));
    }

    public List<Deal> getAllDeals(String orgId) {
        return dealRepository.findByOrganizationId(orgId);
    }

    public Deal createOrUpdateDeal(Optional<String> dealId,
                                   CreateUpdateDealDto createUpdateDealDto,
                                   Organization organization) {
        Deal deal;

        // If a deal ID is provided, fetch the existing deal; otherwise, create a new instance.
        if (dealId.isPresent()) {
            deal = dealRepository.findById(dealId.get())
                    .orElseThrow(() -> new RuntimeException("Deal with ID " + dealId.get() + " not found."));
        } else {
            deal = new Deal();
        }

        // Set properties from DTO
        deal.setName(createUpdateDealDto.getName());
        deal.setEmail(createUpdateDealDto.getEmail());
        deal.setStage(createUpdateDealDto.getStage());
        deal.setValue(createUpdateDealDto.getValue());
        deal.setExpectedCloseDate(createUpdateDealDto.getExpectedCloseDate());
        deal.setActualCloseDate(createUpdateDealDto.getActualCloseDate());
        deal.setStatus(createUpdateDealDto.getStatus());
        deal.setPriority(createUpdateDealDto.getPriority());
        deal.setSource(createUpdateDealDto.getSource());
        deal.setNextStep(createUpdateDealDto.getNextStep());
        deal.setNotes(createUpdateDealDto.getNotes());
        deal.setOrganization(organization);

        // Fetch and set the associated lead

        Lead leads = leadRepository.findById((createUpdateDealDto.getLeadId()))
                .orElseThrow(() -> new RuntimeException("Lead with ID " + createUpdateDealDto.getLeadId() + " not found."));
        deal.setLeads(leads);
        return dealRepository.save(deal);
    }

//    public void updateDealStage(String dealId, String newStage) {
//        Deal deal = dealRepository.findById(dealId)
//                .orElseThrow(() -> new RuntimeException("Deal not found with id: " + dealId));
//        deal.setStage(newStage);
//        dealRepository.save(deal);
//    }

    public DealStatsDto getDealStats() {
        List<Deal> allDeals = dealRepository.findAll();

        BigDecimal totalPipelineValue = BigDecimal.ZERO;
        BigDecimal weightedPipelineValue = BigDecimal.ZERO;
        BigDecimal totalClosedWonValue = BigDecimal.ZERO;
        long closedWonThisMonthCount = 0;

        BigDecimal sumDealValue = BigDecimal.ZERO;
        long dealCount = allDeals.size();

        for (Deal deal : allDeals) {
            BigDecimal value = deal.getValue() != null ? deal.getValue() : BigDecimal.ZERO;

            totalPipelineValue = totalPipelineValue.add(value);

            if (deal.getStage() == Deal.Stage.PROSPECTING) {
                weightedPipelineValue = weightedPipelineValue.add(value.multiply(BigDecimal.valueOf(0.1)));
            } else if (deal.getStage() == Deal.Stage.NEW) {
                weightedPipelineValue = weightedPipelineValue.add(value.multiply(BigDecimal.valueOf(0.3)));
            } else if (deal.getStage() == Deal.Stage.PROPOSAL) {
                weightedPipelineValue = weightedPipelineValue.add(value.multiply(BigDecimal.valueOf(0.6)));
            } else if (deal.getStage() == Deal.Stage.CLOSED_WON) {
                weightedPipelineValue = weightedPipelineValue.add(value); // 100% weight
                sumDealValue = sumDealValue.add(value);

                if (deal.getActualCloseDate() != null &&
                        deal.getActualCloseDate().toLocalDate().getMonth() == LocalDate.now().getMonth() &&
                        deal.getActualCloseDate().toLocalDate().getYear() == LocalDate.now().getYear()) {
                    closedWonThisMonthCount++;
                    totalClosedWonValue = totalClosedWonValue.add(value);
                }
            }
        }

        DealStatsDto stats = new DealStatsDto();
        stats.setTotalPipelineValue(totalPipelineValue);
        stats.setWeightedPipelineValue(weightedPipelineValue);
        stats.setTotalDeals(dealCount);

        DealStatsDto.DealsWonThisMonth wonThisMonth = new DealStatsDto.DealsWonThisMonth();
        wonThisMonth.setCount(closedWonThisMonthCount);
        wonThisMonth.setValue(totalClosedWonValue);
        stats.setDealsWonThisMonth(wonThisMonth);

        DealStatsDto.AvgDealSize avgDealSize = new DealStatsDto.AvgDealSize();
        BigDecimal average = dealCount == 0 ? BigDecimal.ZERO :
                sumDealValue.divide(BigDecimal.valueOf(dealCount), 2, RoundingMode.HALF_UP);
        avgDealSize.setValue(average);
        avgDealSize.setPercentChange(0); // implement trend logic if needed
        stats.setAvgDealSize(avgDealSize);

        return stats;
    }

    public void updateDealStage(String dealId, String newStageStr) {
        Deal deal = dealRepository.findById(dealId)
                .orElseThrow(() -> new RuntimeException("Deal not found with id: " + dealId));

        Deal.Stage newStage;
        try {
            newStage = Deal.Stage.valueOf(newStageStr.toUpperCase()); // Convert string to enum
        } catch (IllegalArgumentException e) {
            throw new RuntimeException("Invalid stage value: " + newStageStr);
        }

        deal.setStage(newStage);
        dealRepository.save(deal);
    }

    public void deleteDeal(String id) {
        dealRepository.deleteById(id);
    }
}

