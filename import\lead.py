import pandas as pd
import mysql.connector
from mysql.connector import Error
import uuid
import re

# Load Excel file
file_path = 'June Email - 2024.xlsx'
excel_data = pd.ExcelFile(file_path)

# MySQL database connection details
host = 'localhost'
user = 'root'
password = 'root'
database = 'crm'

try:
    connection = mysql.connector.connect(
        host=host,
        user=user,
        password=password,
        database=database
    )
    if connection.is_connected():
        cursor = connection.cursor()

        # Fetch user IDs based on first names in the user table
        cursor.execute("SELECT first_name, id FROM user")
        user_first_name_to_id = {first_name: user_id for first_name, user_id in cursor.fetchall()}

        # Define insert query with ON DUPLICATE KEY UPDATE
        insert_query = """
        INSERT INTO leads 
        (id, name, job, email, leaddate, phonenumber, status, linkedin, website, region, empcount, 
        verified, messagesent, comments, sentby, last_updated_date_time, company_id, industry_id) 
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ON DUPLICATE KEY UPDATE 
            name = VALUES(name),
            job = VALUES(job),
            email = VALUES(email),
            leaddate = VALUES(leaddate),
            status = VALUES(status),
            linkedin = VALUES(linkedin),
            website = VALUES(website),
            region = VALUES(region),
            empcount = VALUES(empcount),
            verified = VALUES(verified),
            messagesent = VALUES(messagesent),
            comments = VALUES(comments),
            sentby = VALUES(sentby),
            last_updated_date_time = VALUES(last_updated_date_time),
            company_id = VALUES(company_id),
            industry_id = VALUES(industry_id)
        """

        # Process each sheet in the Excel file
        for sheet_name in excel_data.sheet_names:
            # Load data from the current sheet
            df = pd.read_excel(excel_data, sheet_name=sheet_name)
            df.columns = [column.lower().replace(" ", "_") for column in df.columns]

            # Add default values and generate UUIDs for leads
            df['id'] = [str(uuid.uuid4()) for _ in range(len(df))]
            df['job'] = 'it'
            df['status'] = 'New'
            df['verified'] = df['verified'].apply(lambda x: 1 if x in ['Yes', True, 1] else 0) if 'verified' in df.columns else 0
            df['messagesent'] = df['mail_sent'].apply(lambda x: 1 if x == 'Done' else 0) if 'mail_sent' in df.columns else 0

            # Standardize phone numbers to xxxxxxxxxx format
            def standardize_phone_number(phone):
                if pd.isnull(phone):
                    return None
                phone = re.sub(r'\D', '', phone)
                return phone[-10:].zfill(10) if len(phone) >= 10 else phone.zfill(10)

            df['phonenumber'] = df['phone_number'].astype(str).apply(standardize_phone_number)

            # Ensure lead_name is non-null and truncated to 30 characters
            df['lead_name'] = df['lead_name'].fillna('Unknown').astype(str).str[:30]
            df['email_id'] = df['email_id'].astype(str).str[:50] if 'email_id' in df.columns else None
            df['leaddate'] = pd.to_datetime(df['date'], errors='coerce', dayfirst=True).dt.date

            # Replace NaN with None for MySQL compatibility
            df = df.where(pd.notnull(df), None)

            # Map the first name (sheet name) to user_id for 'sentby'
            df['sentby'] = user_first_name_to_id.get(sheet_name, None)

            # Insert rows into leads table
            for _, row in df.iterrows():
                # Execute the insert query with row access syntax for each field
                cursor.execute(insert_query, (
                    row['id'],
                    row['lead_name'],           # assuming 'lead_name' maps to 'name'
                    row['job'],
                    row.get('email_id'),         # assuming 'email_id' corresponds to 'email'
                    row.get('leaddate'),
                    row.get('phonenumber'),
                    row.get('status'),
                    row.get('lead_link'),        # assuming 'lead_link' maps to 'linkedin'
                    row.get('website_link'),     # assuming 'website_link' maps to 'website'
                    row.get('region'),
                    row.get('employee_count'),   # assuming 'employee_count' maps to 'empcount'
                    row.get('verified'),
                    row.get('messagesent'),
                    row.get('comments'),
                    row.get('sentby'),           # user ID based on first name from sheet name
                    None,                        # last_updated_date_time (NULL by default)
                    None,                        # company_id (placeholder)
                    None                         # industry_id (placeholder)
                ))

            # Commit for each sheet
            connection.commit()

        print("Leads inserted successfully with 'sentby' mapped to user IDs.")

except Error as e:
    print("Error while connecting to MySQL:", e)
finally:
    if connection.is_connected():
        cursor.close()
        connection.close()
        print("MySQL connection is closed")
