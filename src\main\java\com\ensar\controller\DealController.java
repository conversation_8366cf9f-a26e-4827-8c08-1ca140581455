



package com.ensar.controller;

import com.ensar.entity.Deal;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateDealDto;
import com.ensar.request.dto.DealStatsDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.DealService;

import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "Deal Management")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/deals")
@Tag(name = "Deal Management")
public class DealController {

    private final DealService dealService;

    @Autowired
    public DealController(DealService dealService) {
        this.dealService = dealService;
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Deal> getDealById(@PathVariable String id) {
        Deal deal = dealService.getDealById(id);
        return ResponseEntity.ok(deal);
    }

    @GetMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Map<String, List<Deal>>> getAllDeals(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<Deal> dealList = dealService.getAllDeals(orgId);
        Map<String, List<Deal>> response = new HashMap<>();
        response.put("deals", dealList);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Deal> createDeal(@Valid @RequestBody CreateUpdateDealDto createUpdateDealDto,
                                           @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Deal deal = dealService.createOrUpdateDeal(Optional.empty(), createUpdateDealDto,organization);
        return ResponseEntity.ok(deal);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Deal> updateDeal(@PathVariable String id,
                                           @Valid @RequestBody CreateUpdateDealDto createUpdateDealDto,
                                           @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Deal deal = dealService.createOrUpdateDeal(Optional.of(id), createUpdateDealDto,organization);
        return ResponseEntity.ok(deal);
    }

//    @PatchMapping("/{id}/stage")
//    public ResponseEntity<?> updateDealStage(
//            @PathVariable String id,
//            @RequestBody @Valid String stageStr
//    ) {
//
//        dealService.updateDealStage(id, stageStr);
//        return ResponseEntity.ok().build();
//    }

    @PatchMapping("/{id}/stage")
    public ResponseEntity<?> updateDealStage(
            @PathVariable String id,
            @RequestBody Map<String, String> payload
    ) {
        String stage = payload.get("stage");
        dealService.updateDealStage(id, stage);
        return ResponseEntity.ok().build();
    }


    @GetMapping("/stats")
    public DealStatsDto getDealStats() {
        return dealService.getDealStats();
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Void> deleteDeal(@PathVariable String id) {
        dealService.deleteDeal(id);
        return ResponseEntity.ok().build();
    }
}
