import pandas as pd
import mysql.connector
from mysql.connector import <PERSON>rror
import uuid

# Load Excel file
file_path = 'US Search information.xlsx'
excel_data = pd.read_excel(file_path)

# MySQL database connection details
host = 'localhost'
user = 'root'
password = 'root'
database = 'crm'

# Define the insert query without specifying `created_date_time`
insert_query = """
INSERT INTO companies
(id, company_name, address, website, phone_number, type_of_search, social_info, is_e_commerce, site_quality)
VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
"""

# Allowed values for `type_of_search` and `site_quality`
allowed_type_of_search = {'Organic', 'Paid', 'Direct', 'Referral', 'Social'}
allowed_site_quality = {'High', 'Medium', 'Low'}

# Helper function to clean `type_of_search` values
def clean_type_of_search(value):
    return value if value in allowed_type_of_search else 'Organic'

# Helper function to clean `site_quality` values
def clean_site_quality(value):
    return value if value in allowed_site_quality else 'Medium'

# Helper function to convert `E-Commerce` column to boolean
def convert_to_bool(value):
    return str(value).strip().lower() in ['yes', 'true', '1']

# Required columns with default values if missing
required_columns = {
    'company_name': 'Unknown Company',
    'address': None,
    'website': None,
    'phone_number': None,
    'type_of_search': 'Organic',
    'social_info': None,
    'is_e_commerce': False,
    'site_quality': 'Medium'
}

# Establish a connection to the MySQL database
try:
    connection = mysql.connector.connect(
        host=host,
        user=user,
        password=password,
        database=database
    )

    if connection.is_connected():
        cursor = connection.cursor()

        # Preprocess data
        df = excel_data.rename(columns={
            'S.no': 'serial_number',
            'Company': 'company_name',
            'Address': 'address',
            'website': 'website',
            'Phone no': 'phone_number',
            'Type of search': 'type_of_search',
            'Social Info': 'social_info',
            'E-Commerce': 'is_e_commerce',
            'Site Quality': 'site_quality'
        })

        # Ensure all required columns exist in the DataFrame
        for column, default_value in required_columns.items():
            if column not in df.columns:
                df[column] = default_value

        # Generate UUIDs for primary key 'id'
        df['id'] = [str(uuid.uuid4()) for _ in range(len(df))]

        # Clean `type_of_search` and `site_quality` values
        df['type_of_search'] = df['type_of_search'].apply(clean_type_of_search)
        df['site_quality'] = df['site_quality'].apply(clean_site_quality)

        # Convert 'E-Commerce' to boolean if column exists
        df['is_e_commerce'] = df['is_e_commerce'].apply(convert_to_bool)

        # Replace NaN with None for MySQL compatibility
        df = df.where(pd.notnull(df), None)

        # Insert each row into the `companies` table
        success_count, fail_count = 0, 0
        for _, row in df.iterrows():
            try:
                cursor.execute(insert_query, (
                    row['id'],
                    row['company_name'],
                    row['address'],
                    row['website'],
                    row['phone_number'],
                    row['type_of_search'],
                    row['social_info'],
                    row['is_e_commerce'],
                    row['site_quality']
                ))
                success_count += 1
            except Error as e:
                print(f"Failed to insert row: {row['company_name']}, Error: {e}")
                fail_count += 1

        # Commit transaction
        connection.commit()
        print(f"Data insertion completed with {success_count} successful and {fail_count} failed inserts.")

except Error as e:
    print("Error while connecting to MySQL:", e)
finally:
    if connection.is_connected():
        cursor.close()
        connection.close()
        print("MySQL connection is closed")
