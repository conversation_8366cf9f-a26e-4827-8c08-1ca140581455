package com.ensar.util;

import com.ensar.security.EnsarUserDetails;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import com.ensar.config.EnsarSecrets;
import com.ensar.entity.User;

import static com.ensar.util.Constants.AUTHORITIES_KEY;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;
import java.util.function.Function;

@Component
public class JwtTokenUtil implements Serializable {

    @Value("${app.jwt.ttl.minutes}")
    private long tokenTimeToLiveInMinutes;

    @Autowired
    private EnsarSecrets ensarSecrets;

    public String getOrgId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

        // If using JWT, the principal should contain claims
        if (authentication.getPrincipal() instanceof Map) {
            Map<String, Object> claims = (Map<String, Object>) authentication.getPrincipal();
            String orgId = (String) claims.get("organizationId"); // Replace with your claim key
            return orgId;
        } else {
            return "No JWT claims found!";
        }
    }

    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

//    public String getOrganizationIdFromToken(String token) {
//        return getClaimFromToken(token, claims -> claims.get("organizationId", String.class));
//    }

    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);

        return claimsResolver.apply(claims);
    }

    private Claims getAllClaimsFromToken(String token) {
        return Jwts.parser()
                .setSigningKey(ensarSecrets.getJwtSecretKey())
                .parseClaimsJws(token)
                .getBody();
    }

    public Boolean isTokenExpired(String token) {
        final Date expiration = getExpirationDateFromToken(token);
        return expiration.before(new Date());
    }

//    public String createTokenFromAuth(Authentication authentication) {
//        return generateToken(authentication.getName());
//    }

    public String createTokenFromAuth(EnsarUserDetails user) {
        return generateToken(user);
    }

//    public String createTokenFromUser(User user) {
//        return generateToken(user.getEmail());
//    }

//    private String generateToken(String username) {
//        long currentTimestampInMillis = System.currentTimeMillis();
//
//        return Jwts.builder()
//                .setSubject(username)
//                .claim(AUTHORITIES_KEY, "")
//                .signWith(SignatureAlgorithm.HS256, ensarSecrets.getJwtSecretKey())
//                .setIssuedAt(new Date(currentTimestampInMillis))
//                .setExpiration(new Date(currentTimestampInMillis + (tokenTimeToLiveInMinutes * 60 * 1000)))
//                .compact();
//    }

    private String generateToken(EnsarUserDetails user) {
        long currentTimestampInMillis = System.currentTimeMillis();
        String username = user.getUsername();
        String orgId =user.getOrganization().getId();
        return Jwts.builder()
                .setSubject(username)
                .claim("organizationId",orgId)
                .claim(AUTHORITIES_KEY, "")
                .signWith(SignatureAlgorithm.HS256, ensarSecrets.getJwtSecretKey())
                .setIssuedAt(new Date(currentTimestampInMillis))
                .setExpiration(new Date(currentTimestampInMillis + (tokenTimeToLiveInMinutes * 60 * 1000)))
                .compact();
    }
    public Boolean validateToken(String token, UserDetails userDetails) {
        final String username = getUsernameFromToken(token);

        return (username.equals(userDetails.getUsername()) && !isTokenExpired(token));
    }
}
