package com.ensar.controller;

import com.ensar.entity.LeadReply;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateLeadReplyDto;
import com.ensar.response.dto.LeadReplyResponseDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.LeadReplyService;


import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "Lead Replies Mgmt")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/leadreplies")
@Tag(name = "Lead Replies management") // Class-level Tag annotation

public class LeadReplyController {

    @Autowired
    private LeadReplyService leadReplyService;

    @GetMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<List<LeadReplyResponseDto>> getAllLeadReplies(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<LeadReplyResponseDto> leadReplies = leadReplyService.getAllLeadReplies(orgId);
        return ResponseEntity.ok(leadReplies);
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<LeadReplyResponseDto> getLeadReplyById(@PathVariable String id) {
        Optional<LeadReply> leadReply = leadReplyService.getLeadReplyById(id);
        return leadReply.map(reply -> ResponseEntity.ok(leadReplyService.convertToDto(reply)))
                .orElseGet(() -> ResponseEntity.notFound().build());
    }

    @GetMapping("/lead/{leadId}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Map<String,List<LeadReplyResponseDto>>> getLeadRepliesByLeadId(@PathVariable String leadId) {
        List<LeadReplyResponseDto> leadreplies = leadReplyService.getLeadRepliesByLeadId(leadId);
        Map<String, List<LeadReplyResponseDto>> response = new HashMap<>();
        response.put("leadReplies", leadreplies);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<LeadReplyResponseDto> createLeadReply(@RequestBody CreateUpdateLeadReplyDto leadReplyDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        LeadReply savedLeadReply = leadReplyService.createLeadReply(leadReplyDto,organization);
        return ResponseEntity.ok(leadReplyService.convertToDto(savedLeadReply));
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<LeadReplyResponseDto> updateLeadReply(@PathVariable String id, @RequestBody CreateUpdateLeadReplyDto leadReplyDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        if (!leadReplyService.getLeadReplyById(id).isPresent()) {
            return ResponseEntity.notFound().build();
        }
        LeadReply updatedLeadReply = leadReplyService.updateLeadReply(id, leadReplyDto);
        return ResponseEntity.ok(leadReplyService.convertToDto(updatedLeadReply));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Void> deleteLeadReply(@PathVariable String id) {
        leadReplyService.deleteLeadReply(id);
        return ResponseEntity.ok().build();
    }
}
