package com.ensar.controller;



import com.ensar.entity.Organization;
import com.ensar.entity.Project;
import com.ensar.request.dto.CreateUpdateProjectDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.ProjectService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "Project Management")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/projects")
@Tag(name = "Project Management") // Class-level Tag annotation

public class ProjectController {

    private final ProjectService projectService;

    @Autowired
    public ProjectController(ProjectService projectService) {
        this.projectService = projectService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<Project> getProjectById(@PathVariable String id) {
        Project project = projectService.getProjectById(id);
        return ResponseEntity.ok(project);
    }

    @GetMapping("/")
    public ResponseEntity<Map<String, Page<Project>>> getProjectsByPagination(@AuthenticationPrincipal EnsarUserDetails userDetails,
                                                                     @RequestParam(defaultValue = "0") int page,
                                                                     @RequestParam(defaultValue = "5") int size) {
        String orgId = userDetails.getOrganization().getId();
        Page<Project> projectList = projectService.getProjectsByPagination(orgId, page, size);
        Map<String, Page<Project>> response = new HashMap<>();
        response.put("projects", projectList);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/all")
    public ResponseEntity<Map<String, List<Project>>> getAllProjects(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<Project> projectList = projectService.getAllProjects(orgId);
        Map<String, List<Project>> response = new HashMap<>();
        response.put("projects", projectList);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    public ResponseEntity<Project> createProject(@Valid @RequestBody CreateUpdateProjectDto createUpdateProjectDto, @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Project project = projectService.createOrUpdateProject(Optional.empty(), createUpdateProjectDto, organization);
        return ResponseEntity.ok(project);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Project> updateProject(@PathVariable String id,
                                                 @Valid @RequestBody CreateUpdateProjectDto createUpdateProjectDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Project project = projectService.createOrUpdateProject(Optional.of(id), createUpdateProjectDto,organization);
        return ResponseEntity.ok(project);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProject(@PathVariable String id) {
        projectService.deleteProject(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/import")
    public ResponseEntity<Void> importProjects(@RequestParam("file") MultipartFile file) throws IOException {
        projectService.importProjects(file.getInputStream());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportProjects() throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        projectService.exportProjects(outputStream);
        byte[] bytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename("projects.csv")
                .build();
        headers.setContentDisposition(contentDisposition);

        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);
    }
}
