package com.ensar.repository;

import com.ensar.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.jpa.repository.Query;

import com.ensar.entity.Lead;

import java.util.List;

@Repository
public interface LeadRepository extends JpaRepository<Lead, String> {
	@Query("SELECT DISTINCT l.email FROM Lead l WHERE l.email IS NOT NULL")
	List<String> findAllEmails();

	@Query("SELECT DISTINCT l.phoneNumber FROM Lead l WHERE l.phoneNumber IS NOT NULL")
	List<String> findAllPhoneNumbers();

	@Query("SELECT DISTINCT l.linkedin FROM Lead l WHERE l.linkedin IS NOT NULL")
	List<String> findAllLinkedins();

	// Optional existing method for duplicates
	boolean existsByEmailOrPhoneNumberOrLinkedin(String email, String phonenumber, String linkedin);

	List<Lead> findBySentByEmail(String email);

	boolean existsByEmail(String email);

	boolean existsByPhoneNumber(String phonenumber);

	boolean existsByLinkedin(String linkedin);

	boolean existsByFirstnameAndLastname(String firstName, String lastName);

	List<Lead> findByDraftStatus(Boolean draftStatus);

//	Page<Lead> findByOrganizationId(String organizationId, Pageable pageable);

	List<Lead> findByOrganizationId(String organizationId);

	Page<Lead> findByOrganizationId(String organizationId, Pageable pageable);

	Page<Lead> findByOrganizationIdAndSentById(String organizationId, String userId ,Pageable pageable);
}
