SET NAMES utf8;
SET
    time_zone = '+00:00';
SET
    sql_mode = 'NO_AUTO_VALUE_ON_ZERO';


CREATE
    DATABASE IF NOT EXISTS `${schema}` DEFAULT CHARACTER SET utf8
    DEFAULT COLLATE utf8_general_ci;

USE
`${schema}`;

DROP TABLE IF EXISTS `organizations`;

CREATE TABLE `organizations`
(
    `id`                 char(36)    NOT NULL,
    `name`         varchar(50) NULL,
    `description`          varchar(500) NOT NULL,
    `domain`          varchar(50) NOT NULL,
    `logo_img_src`      text NULL,
    `disabled`            boolean     NOT NULL default false,
    `created_date_time`  timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `org_name_unique` (`name`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `roles`;

CREATE TABLE `roles`
(
    `id`                   char(36)     NOT NULL,
    `role_name`          VARCHAR(100) NOT NULL,
    `role_description`     TEXT         NOT NULL,
    `role_permission` ENUM('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER') NOT NULL,
    `created_date_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp NULL DEFAULT NULL,
    `organization_id` CHAR(36) NOT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT fk_role_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE

) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `users`;

CREATE TABLE `users`
(
    `id`                    char(36)    NOT NULL,
    `first_name`            varchar(50) NULL,
    `last_name`             varchar(50) NOT NULL,
    `email`                 varchar(100) NOT NULL,
    `password`              varchar(500) NULL,
    `city`                  varchar(100) NULL,
    `state`                 varchar(100) NULL,
    `country`               varchar(100) NULL,
    `address`               varchar(255) NULL,
    `zip_code`              varchar(20) NULL,
    `phone_number`          varchar(20) NULL,
    `company`               varchar(255) NULL,
    `avatar_url`            varchar(255) NULL,
    `status`                ENUM('Active', 'InActive') NULL DEFAULT 'InActive', -- For tracking account status (e.g., active, disabled)
    `sign_up_method`        VARCHAR(255) DEFAULT NULL,
    `two_factor_secret`     VARCHAR(255) DEFAULT NULL,
    `is_two_factor_enabled` TINYINT(1) NOT NULL DEFAULT 0,
    `is_verified`           boolean      NOT NULL DEFAULT false, -- For account verification status
    `email_verified`        boolean      NOT NULL DEFAULT false, -- Specific email verification status
    `disabled`              boolean      NOT NULL DEFAULT false, -- Account disabled status
    `created_date_time`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp   NULL DEFAULT NULL,
    `role_id`               char(36) NOT NULL references role(`id`),
    `organization_id`       char(36) NOT NULL references organizations(`id`),
    PRIMARY KEY (`id`),
    UNIQUE KEY `user_email_unique` (`email`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

DROP TABLE IF EXISTS `user_password_reset_request`;

CREATE TABLE `user_password_reset_request`
(
    `id`                     char(36)    NOT NULL,
    `user_id`                char(36)    NOT NULL,
    `expire_date_time`       timestamp not null,
    `created_date_time`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp NULL     DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8;


DROP TABLE IF EXISTS `user_login_tracker`;

CREATE TABLE `user_login_tracker`
(
    `id`                     char(36)    NOT NULL,
    `user_email`   char(36) not null,
    `user_ip`          char(36)   NOT NULL,
    `succeeded`          boolean  NOT NULL default false,
    `created_date_time`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp NULL     DEFAULT NULL,
    PRIMARY KEY (`id`)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8;


alter table `users` add column last_login_date_time timestamp null default null;

update `users` u join
(select max(created_date_time) as last_login, user_email from user_login_tracker ult
 where succeeded  =  true group by user_email) as t
 on (t.user_email = u.email)
set u.last_login_date_time = t.last_login
