import requests
import mysql.connector
import pandas as pd
import uuid
from concurrent.futures import ThreadPoolExecutor, as_completed

def read_addresses_from_excel(file_path):
    """Read all addresses from the specified Excel file."""
    excel_data = pd.ExcelFile(file_path)
    sheet_data = excel_data.parse(excel_data.sheet_names[0])

    # Read all addresses from the column and drop NaN values
    addresses = sheet_data.iloc[1:, 2].dropna().reset_index(drop=True)
    print(f"Total addresses found: {len(addresses)}")
    return addresses

def get_location_coordinates(address, api_key):
    """Get latitude and longitude of an address using Google Maps Geocoding API."""
    geocode_url = "https://maps.googleapis.com/maps/api/geocode/json"
    params = {'address': address, 'key': api_key}
    response = requests.get(geocode_url, params=params)
    geocode_data = response.json()
    if geocode_data['status'] == 'OK':
        location = geocode_data['results'][0]['geometry']['location']
        return f"{location['lat']},{location['lng']}"
    else:
        raise ValueError(f"Failed to fetch coordinates for {address}: {geocode_data['status']}")

def get_shops_by_address(address, api_key, radius=1000):
    """Retrieve shop information near a given address using Google Places API."""
    location = get_location_coordinates(address, api_key)
    places_url = "https://maps.googleapis.com/maps/api/place/nearbysearch/json"
    params = {'location': location, 'radius': radius, 'type': 'store', 'key': api_key}
    response = requests.get(places_url, params=params)
    places = response.json()
    shops = []
    if places['status'] == 'OK':
        for place in places['results']:
            basic_info = {
                'id': str(uuid.uuid4()),  # Generate a UUID for each shop
                'name': place.get('name'),
                'address': place.get('vicinity'),
                'place_id': place.get('place_id'),
                'rating': place.get('rating'),
                'latitude': place['geometry']['location']['lat'],
                'longitude': place['geometry']['location']['lng'],
                'user_ratings_total': place.get('user_ratings_total')
            }
            detailed_info = get_place_details(basic_info['place_id'], api_key)
            shops.append({**basic_info, **detailed_info, 'place_type': 'store'})
    return shops

def get_place_details(place_id, api_key):
    """Fetch place details (phone, website, opening hours) using Google Places API."""
    details_url = "https://maps.googleapis.com/maps/api/place/details/json"
    params = {'place_id': place_id, 'key': api_key, 'fields': 'formatted_phone_number,website,opening_hours'}
    response = requests.get(details_url, params=params)
    details = response.json()
    if details['status'] == 'OK':
        result = details['result']
        return {
            'phone_number': result.get('formatted_phone_number'),
            'website': result.get('website'),
            'opening_hours': result.get('opening_hours', {}).get('weekday_text', [])
        }
    else:
        return {'phone_number': None, 'website': None, 'opening_hours': []}

def fetch_shops_parallel(addresses, api_key):
    """Fetch shop data in parallel for a list of addresses."""
    shops = []
    with ThreadPoolExecutor(max_workers=10) as executor:  # Adjust max_workers as needed
        future_to_address = {executor.submit(get_shops_by_address, address, api_key): address for address in addresses}
        for future in as_completed(future_to_address):
            address = future_to_address[future]
            try:
                result = future.result()
                print(f"Fetched {len(result)} shops for address: {address}")
                shops.extend(result)
            except Exception as e:
                print(f"Error fetching data for {address}: {e}")
    return shops

def store_shops_in_mysql(shops):
    """Store retrieved shop information in a MySQL database with UUIDs."""
    print("Storing shops in the MySQL database...")
    connection = mysql.connector.connect(
        host='localhost',
        user='root',
        password='root',  # Replace with your MySQL password
        database='crm'  # Replace with your MySQL database
    )
    cursor = connection.cursor()

    # Create a table for shops
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS Shops (
            id CHAR(36) PRIMARY KEY,
            name VARCHAR(255),
            address VARCHAR(255),
            place_id VARCHAR(255),
            rating FLOAT,
            latitude FLOAT,
            longitude FLOAT,
            user_ratings_total INT,
            phone_number VARCHAR(20),
            website VARCHAR(255),
            opening_hours TEXT,
            place_type VARCHAR(50)
        )
    ''')

    # Insert shop data with UUIDs
    for shop in shops:
        cursor.execute('''
            INSERT INTO Shops (id, name, address, place_id, rating, latitude, longitude,
            user_ratings_total, phone_number, website, opening_hours, place_type)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        ''', (
            shop['id'], shop['name'], shop['address'], shop['place_id'],
            shop['rating'], shop['latitude'], shop['longitude'],
            shop['user_ratings_total'], shop['phone_number'], shop['website'],
            '\n'.join(shop['opening_hours']), shop['place_type']
        ))

    connection.commit()
    cursor.close()
    connection.close()
    print("All shop data has been stored successfully.")

# Main script execution
if __name__ == "__main__":
    api_key = 'AIzaSyAjYmYwNBvnsOD1V6kV9GllY5P5GxksWuM'  # Replace with your actual Google API key
    file_path = 'Shops_and_related_websites_in_Illinois2.xlsx'  # Update with your Excel file path

    # Read all addresses from the Excel file
    addresses = read_addresses_from_excel(file_path)

    # Fetch shop data in parallel
    all_shops = fetch_shops_parallel(addresses, api_key)

    # Store all fetched shop data in the MySQL database
    store_shops_in_mysql(all_shops)
    print("Shop data fetched and stored successfully.")
