package com.ensar.service;

import com.ensar.entity.Role;
import com.ensar.entity.User;
import com.ensar.repository.UserRepository;
import jakarta.transaction.Transactional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.ensar.entity.Organization;
import com.ensar.repository.OrganizationRepository;
import com.ensar.request.dto.CreateUpdateOrgDto;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class OrganizationService {

    private OrganizationRepository organizationRepository;
    private final UserRepository userRepository;

    @Autowired
    public OrganizationService(OrganizationRepository organizationRepository, UserRepository userRepository) {
        this.organizationRepository = organizationRepository;
        this.userRepository = userRepository;
    }

    public Organization getOrganizationById(String id) {
        Optional<Organization> organizationOptional = organizationRepository.findById(id);

        if (!organizationOptional.isPresent())
            throw new RuntimeException("Organization with " + id + " not found.");
        return organizationOptional.get();
    }
    public User getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }
    public User getLoggedInUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        return getUserByEmail(authentication.getName());
    }

//    public List<Organization> getAllOrganizations() {
//
//        User loggedInUser = getLoggedInUser();
//        Role.RolePermission rolePermission = loggedInUser.getRole().getRolePermission();
//        if (Role.RolePermission.ROLE_SUPER_ADMIN.equals(rolePermission)) {
//            return organizationRepository.findAll();
//        } else if (Role.RolePermission.ROLE_ADMIN.equals(rolePermission)) {
//            // Assuming that an ADMIN user can only see organizations they are associated with
//            return List.of(loggedInUser.getOrganization());
//        } else if (Role.RolePermission.ROLE_USER.equals(rolePermission)) {
//            // Assuming that a regular USER cannot view organizations
//            return List.of();
//        }
//        // Return an empty list for roles that are not handled
//        return List.of();
//    }

    public Page<Organization> getAllOrganizations(int page, int size) {
        User loggedInUser = getLoggedInUser();
        Role.RolePermission rolePermission = loggedInUser.getRole().getRolePermission();
        Pageable pageable = PageRequest.of(page, size);

        if (Role.RolePermission.ROLE_SUPER_ADMIN.equals(rolePermission)) {
            return organizationRepository.findAll(pageable);
        } else if (Role.RolePermission.ROLE_ADMIN.equals(rolePermission)) {
            // Assuming ADMIN can see only their own organization
            // So we return a single organization as a Page
            Organization org = loggedInUser.getOrganization();
            List<Organization> list = org == null ? List.of() : List.of(org);
            return new PageImpl<>(list, pageable, list.size());
        } else if (Role.RolePermission.ROLE_USER.equals(rolePermission)) {
            // Regular USER cannot view organizations, so return empty page
            return Page.empty(pageable);
        }
        // For unhandled roles, return empty page
        return Page.empty(pageable);
    }

    public Organization createOrUpdateOrganization(Optional<String> orgId, CreateUpdateOrgDto createUpdateOrgDto) {
        Organization organization;
        if (orgId.isPresent()) {
            organization = organizationRepository.getById(orgId.get());
            if (organization == null)
                throw new RuntimeException("Organization with id " + orgId.get() + " not found");
        } else {
            organization = new Organization();
            if (organizationRepository.existsByName(createUpdateOrgDto.getName()))
                throw new RuntimeException("Organization with name " + createUpdateOrgDto.getName() + " already exists.");
        }

        organization.setName(createUpdateOrgDto.getName());
        organization.setDescription(createUpdateOrgDto.getDescription());
        organization.setDomain(createUpdateOrgDto.getDomain());
        organization.setLogoImgSrc(createUpdateOrgDto.getLogoImgSrc());
        organization.setDisabled(createUpdateOrgDto.isDisabled());

        organization = organizationRepository.save(organization);
        return organization;
    }
    public void deleteOrganization(String id) {
        organizationRepository.deleteById(id);
    }

    public void enableOrDisableOrgs(List<String> orgIdList, final boolean disabled) {
        orgIdList.forEach(id -> {
            organizationRepository.getById(id).setDisabled(disabled);
        });
    }
}
