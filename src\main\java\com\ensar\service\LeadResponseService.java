package com.ensar.service;

import com.ensar.entity.Lead;
import com.ensar.entity.LeadReply;
import com.ensar.entity.LeadResponse;
import com.ensar.entity.Organization;
import com.ensar.repository.LeadReplyRepository;
import com.ensar.repository.LeadRepository;
import com.ensar.repository.LeadResponseRepository;
import com.ensar.request.dto.CreateUpdateLeadResponseDto;
import com.ensar.response.dto.LeadResponseResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class LeadResponseService {

    @Autowired
    private LeadResponseRepository leadResponseRepository;

    @Autowired
    private LeadRepository leadRepository;

    @Autowired
    private LeadReplyRepository leadReplyRepository;

    // Create a new LeadResponse associated with a specific LeadReply
    public LeadResponse createLeadResponse(CreateUpdateLeadResponseDto dto, Organization organization) {
        LeadReply reply = leadReplyRepository.findById(dto.getReplyId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                        "Reply not found with id " + dto.getReplyId()));
//        Lead lead = leadRepository.findById(dto.getLeadId()).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + dto.getLeadId()));


        LeadResponse response = new LeadResponse();
//        response.setLead(lead);
        response.setReply(reply);
        response.setResponse(dto.getResponse());
        response.setRespondAt(dto.getRespondAt());
        response.setOrganization(organization);

        return leadResponseRepository.save(response);
    }

    // Retrieve all LeadResponses for a specific replyId
//    public List<LeadResponseResponseDto> getResponsesForReply(String replyId) {
//        return leadResponseRepository.findByReplyId(replyId).stream()
//                .map(this::convertToDto)
//                .collect(Collectors.toList());
//    }


    public List<LeadResponseResponseDto> getResponsesForReply(String replyId) {
        System.out.println("Fetching Lead Responses for replyId: " + replyId);
        List<LeadResponse> leadResponses = leadResponseRepository.findByReplyId(replyId);
        System.out.println("Lead Responses fetched: " + leadResponses.size());
        return leadResponses.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    // Retrieve all LeadResponses
    public List<LeadResponseResponseDto> getAllLeadResponses(String orgId) {
        return leadResponseRepository.findByOrganizationId(orgId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    // Retrieve a specific LeadResponse by its ID
    public Optional<LeadResponse> getLeadResponseById(String  id) {
        return leadResponseRepository.findById(id);
    }

    // Convert a LeadResponse entity to a DTO for API responses
//    public LeadResponseResponseDto convertToDto(LeadResponse response) {
//        LeadResponseResponseDto dto = new LeadResponseResponseDto();
//        dto.setId(response.getId());
////        dto.setLeadId(response.getLead().getId());
//        dto.setReplyId(response.getReply().getId());
//        dto.setResponse(response.getResponse());
//        dto.setRespondAt(response.getRespondAt().toString());
//
//        return dto;
//    }

    public LeadResponseResponseDto convertToDto(LeadResponse response) {
        LeadResponseResponseDto dto = new LeadResponseResponseDto();
        dto.setId(response.getId());
        dto.setReplyId(response.getReply().getId());
        dto.setResponse(response.getResponse());
        dto.setRespondAt(response.getRespondAt().toString()); // Check if respondAt is being set correctly
        return dto;
    }


    // Delete a LeadResponse by its ID
    public void deleteLeadResponse(String  responseId) {
        if (!leadResponseRepository.existsById(responseId)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                    "Lead response not found with id " + responseId);
        }
        leadResponseRepository.deleteById(responseId);
    }

    // Update an existing LeadResponse
    public LeadResponse updateLeadResponse(String  responseId, CreateUpdateLeadResponseDto dto) {
        LeadResponse response = leadResponseRepository.findById(responseId)
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                        "Lead response not found with id " + responseId));

        LeadReply reply = leadReplyRepository.findById(dto.getReplyId())
                .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                        "Reply not found with id " + dto.getReplyId()));
//        Lead lead = leadRepository.findById(dto.getLeadId()).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + dto.getLeadId()));

//        response.setLead(lead);
        response.setReply(reply);
        response.setResponse(dto.getResponse());
        response.setRespondAt(dto.getRespondAt());

        return leadResponseRepository.save(response);
    }
}
