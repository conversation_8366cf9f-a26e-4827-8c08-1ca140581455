package com.ensar.config;

import com.ensar.entity.Organization;
import com.ensar.entity.Role;
import com.ensar.entity.User;
import com.ensar.repository.OrganizationRepository;
import com.ensar.repository.RoleRepository;
import com.ensar.repository.UserRepository;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.UserService;
import com.ensar.util.JwtTokenUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClient;
import org.springframework.security.oauth2.client.OAuth2AuthorizedClientService;
import org.springframework.security.oauth2.client.authentication.OAuth2AuthenticationToken;
import org.springframework.security.oauth2.core.user.DefaultOAuth2User;
import org.springframework.security.web.authentication.SavedRequestAwareAuthenticationSuccessHandler;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class OAuth2LoginSuccessHandler extends SavedRequestAwareAuthenticationSuccessHandler {

    @Autowired
    private final UserService userService;

    @Autowired
    private final OrganizationRepository organizationRepository;

    @Autowired
    private final UserRepository userRepository;

    @Autowired
    private final JwtTokenUtil jwtUtils;

    @Autowired
    private final RoleRepository roleRepository;

    @Autowired
    private final OAuth2AuthorizedClientService authorizedClientService;

    @Value("${frontend.url}")
    private String frontendUrl;

    String username;
    String idAttributeKey;

    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws ServletException, IOException {
        OAuth2AuthenticationToken oAuth2AuthenticationToken = (OAuth2AuthenticationToken) authentication;
        String finalEmail;
        if ("github".equals(oAuth2AuthenticationToken.getAuthorizedClientRegistrationId()) ||
                "google".equals(oAuth2AuthenticationToken.getAuthorizedClientRegistrationId())) {

            DefaultOAuth2User principal = (DefaultOAuth2User) authentication.getPrincipal();
            Map<String, Object> attributes = principal.getAttributes();
            System.out.println(attributes);

            String email = (String) attributes.get("email");
            String name = (String) attributes.get("name");

            String registrationId = oAuth2AuthenticationToken.getAuthorizedClientRegistrationId();

            if ("github".equals(registrationId)) {
                username = (String) attributes.get("login");
                idAttributeKey = "id";

                // Fallback: fetch email from GitHub if not available in attributes
                if (email == null || email.isBlank()) {
                    OAuth2AuthorizedClient client = authorizedClientService.loadAuthorizedClient(
                            registrationId, oAuth2AuthenticationToken.getName());

                    if (client != null) {
                        String accessToken = client.getAccessToken().getTokenValue();
                        email = fetchPrimaryVerifiedGitHubEmail(accessToken);
                    }
                }

            } else if ("google".equals(registrationId)) {
                email = email != null ? email : "";
                username = email.split("@")[0];
                idAttributeKey = "sub";
            } else {
                username = "";
                idAttributeKey = "id";
            }

            System.out.println("HELLO OAUTH: " + email + " : " + name + " : " + username);

            finalEmail = email;
            userService.findByEmail(finalEmail)
                    .ifPresentOrElse(user -> {
                        DefaultOAuth2User oauthUser = new DefaultOAuth2User(
                                List.of(new SimpleGrantedAuthority(user.getRole().getRolePermission().name())),
                                attributes,
                                idAttributeKey
                        );
                        Authentication securityAuth = new OAuth2AuthenticationToken(
                                oauthUser,
                                List.of(new SimpleGrantedAuthority(user.getRole().getRolePermission().name())),
                                registrationId
                        );
                        SecurityContextHolder.getContext().setAuthentication(securityAuth);
                    }, () -> {
                        User newUser = new User();
                        List<Role> userRole = roleRepository.findByRolePermission(Role.RolePermission.ROLE_USER);
                        if (userRole != null && !userRole.isEmpty()) {
                            newUser.setRole(userRole.get(0));
                        } else {
                            throw new RuntimeException("Default role not found");
                        }
                        Organization organization = organizationRepository.findByName("Demo Org");
                        newUser.setEmail(finalEmail);
                        newUser.setFirstName(username);
                        newUser.setLastName(" ");
                        newUser.setOrganization(organization);
                        userService.registerUser(newUser);

                        DefaultOAuth2User oauthUser = new DefaultOAuth2User(
                                List.of(new SimpleGrantedAuthority(newUser.getRole().getRolePermission().name())),
                                attributes,
                                idAttributeKey
                        );
                        Authentication securityAuth = new OAuth2AuthenticationToken(
                                oauthUser,
                                List.of(new SimpleGrantedAuthority(newUser.getRole().getRolePermission().name())),
                                registrationId
                        );
                        SecurityContextHolder.getContext().setAuthentication(securityAuth);
                    });
        }

        this.setAlwaysUseDefaultTargetUrl(true);

        // JWT TOKEN LOGIC
        DefaultOAuth2User oauth2User = (DefaultOAuth2User) authentication.getPrincipal();
        Map<String, Object> attributes = oauth2User.getAttributes();
        System.out.println("demo"+ attributes);
        String email = (String) attributes.get("email");

        String registrationId = oAuth2AuthenticationToken.getAuthorizedClientRegistrationId();

        if (email == null || email.isBlank()) {
            OAuth2AuthorizedClient client = authorizedClientService.loadAuthorizedClient(
                    registrationId, oAuth2AuthenticationToken.getName());

            if (client != null) {
                String accessToken = client.getAccessToken().getTokenValue();
                email = fetchPrimaryVerifiedGitHubEmail(accessToken);
            }
        }
        System.out.println("OAuth2LoginSuccessHandler: " + username + " : " + email);

        Set<SimpleGrantedAuthority> authorities = new HashSet<>(oauth2User.getAuthorities().stream()
                .map(authority -> new SimpleGrantedAuthority(authority.getAuthority()))
                .collect(Collectors.toList()));

        User user = userService.findByEmail(email).orElseThrow(() ->
                new RuntimeException("User not found"));

        authorities.add(new SimpleGrantedAuthority(user.getRole().getRolePermission().name()));

        EnsarUserDetails userDetails = new EnsarUserDetails(user);
        String jwtToken = jwtUtils.createTokenFromAuth(userDetails);
        String targetUrl;
        if("github".equals(registrationId)){
            targetUrl = UriComponentsBuilder.fromUriString(frontendUrl + "/oauth2/github/redirect")
                    .queryParam("token", jwtToken)
                    .build().toUriString();

        }else if("google".equals(registrationId)) {
             targetUrl = UriComponentsBuilder.fromUriString(frontendUrl + "/oauth2/google/redirect")
                    .queryParam("token", jwtToken)
                    .build().toUriString();
        } else{
            targetUrl = UriComponentsBuilder.fromUriString(frontendUrl + "/oauth2/redirect")
                    .queryParam("token", jwtToken)
                    .build().toUriString();
        }

        this.setDefaultTargetUrl(targetUrl);
        super.onAuthenticationSuccess(request, response, authentication);
    }

    private String fetchPrimaryVerifiedGitHubEmail(String accessToken) {
        RestTemplate restTemplate = new RestTemplate();
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(accessToken);
        headers.setAccept(List.of(MediaType.APPLICATION_JSON));
        HttpEntity<String> entity = new HttpEntity<>(headers);

        ResponseEntity<List<Map<String, Object>>> response = restTemplate.exchange(
                "https://api.github.com/user/emails",
                HttpMethod.GET,
                entity,
                new ParameterizedTypeReference<>() {}
        );

        if (response.getStatusCode().is2xxSuccessful()) {
            List<Map<String, Object>> emails = response.getBody();
            if (emails != null) {
                for (Map<String, Object> emailEntry : emails) {
                    boolean primary = Boolean.TRUE.equals(emailEntry.get("primary"));
                    boolean verified = Boolean.TRUE.equals(emailEntry.get("verified"));
                    if (primary && verified) {
                        return (String) emailEntry.get("email");
                    }
                }
            }
        }

        return null;
    }
}
