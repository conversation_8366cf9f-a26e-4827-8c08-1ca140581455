package com.ensar.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.sql.Timestamp;

@Entity(name = "users")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class User extends BaseEntity {

    @Column(name = "first_name")
    private String firstName;

    @Column(name = "last_name")
    private String lastName;

    @Column(name = "email")
    private String email;

    @Column(name = "password", nullable = true)
    @JsonIgnore
    private String password;

    @Column(name = "disabled")
    private boolean disabled;

    @Column(name = "sign_up_method")
    private String signUpMethod;

    @Column(name = "two_factor_secret")
    private String twoFactorSecret;

    @Column(name = "is_two_factor_enabled")
    private boolean isTwoFactorEnabled;

    @ManyToOne
    @JoinColumn(name = "role_id", referencedColumnName = "id")
    private Role role;

    @Column(name = "city")
    private String city;

    @Column(name = "state")
    private String state;

    @Column(name = "country")
    private String country;

    @Column(name = "address")
    private String address;

    @Column(name = "zip_code")
    private String zipCode;

    @Column(name = "phone_number")
    private String phoneNumber;

    @Column(name = "company")
    private String company;

    @Column(name = "avatar_url")
    private String avatarUrl;

    @Column(name = "status")
    private String status; // Account status (e.g., active, disabled)

    @Column(name = "is_verified")
    private boolean verified;

    @Column(name = "email_verified")
    private boolean emailVerified;

    @Column(name = "last_login_date_time")
    private Timestamp lastLoginDateTime;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;
}
