package com.ensar.request.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;

import java.sql.Date;

@Schema(description = "Parameters required to create/update lead reply")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateLeadReplyDto {

    @Schema(description = "Lead ID", required = true)
//    @NotBlank(message = "Lead ID is required")
    private String leadId;

    @Schema(description = "Reply Text", required = true)
    @NotBlank(message = "Reply Text is required")
    private String replyText;

    @Schema(description = "Replier User ID", required = true)
//    @NotBlank(message = "Replier User ID is required")
    private String replierId;

    @Schema(description = "reply At Timestamp", required = true)
    @NotBlank(message = "reply At is required")
    private Date replyAt;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;

}
