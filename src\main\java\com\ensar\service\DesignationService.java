package com.ensar.service;

import com.ensar.entity.Designation;
import com.ensar.entity.Organization;
import com.ensar.repository.DesignationRepository;
import com.ensar.request.dto.CreateUpdateDesignationDto;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;

@Service
@Log4j2
@Transactional
public class DesignationService {

    private final DesignationRepository designationRepository;

    @Autowired
    public DesignationService(DesignationRepository designationRepository) {
        this.designationRepository = designationRepository;
    }

    public Designation getDesignationById(String id) {
        Optional<Designation> designationOptional = designationRepository.findById(id);
        if (!designationOptional.isPresent())
            throw new RuntimeException("Designation with ID " + id + " not found.");

        return designationOptional.get();
    }

    public List<Designation> getAllDesignations(String orgId) {
        return designationRepository.findByOrganizationId(orgId);
    }

    public Designation getDesignationByName(String name) {
        return designationRepository.findByName(name);
    }

    public boolean designationExistsByName(String name) {
        return designationRepository.existsByName(name);
    }

    public List<Designation> getDesignationsByDescriptionContaining(String keyword) {
        return designationRepository.findByDescriptionContaining(keyword);
    }

    public Designation createOrUpdateDesignation(Optional<String> designationId,
                                                 CreateUpdateDesignationDto createUpdateDesignationDto,
                                                 Organization organization) {
        Designation designation;
        if (designationId.isPresent()) {
            designation = designationRepository.findById(designationId.get())
                    .orElseThrow(() -> new RuntimeException("Designation with ID " + designationId.get() + " not found"));
        } else {
            designation = new Designation();
        }

        designation.setName(createUpdateDesignationDto.getName());
        designation.setDescription(createUpdateDesignationDto.getDescription());
        designation.setOrganization(organization);

        return designationRepository.save(designation);
    }

    public void deleteDesignation(String id) {
        designationRepository.deleteById(id);
    }

    public void importDesignations(InputStream inputStream) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        List<Designation> designations = new ArrayList<>();
        Map<String, Integer> headerMap = new HashMap<>();

        if ((line = reader.readLine()) != null) {
            String[] headers = line.split(",");
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
        } else {
            throw new IOException("Empty CSV file");
        }

        while ((line = reader.readLine()) != null) {
            String[] fields = line.split(",");

            if (fields.length != headerMap.size()) {
                throw new IOException("Invalid CSV format. Each line must have the same number of fields as the header.");
            }

            Designation designation = new Designation();
            try {
                String name = fields[headerMap.get("name")];
                String description = fields[headerMap.get("description")];

                designation.setName(name);
                designation.setDescription(description);
            } catch (Exception e) {
                throw new IOException("Error processing line: " + line, e);
            }

            designations.add(designation);
        }
        designationRepository.saveAll(designations);
    }

    public void exportDesignations(OutputStream outputStream) throws IOException {
        List<Designation> designations = designationRepository.findAll();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("Name,Description\n");
        for (Designation designation : designations) {
            writer.write(String.format("%s,%s\n",
                    designation.getName(),
                    designation.getDescription()));
        }
        writer.flush();
    }
}
