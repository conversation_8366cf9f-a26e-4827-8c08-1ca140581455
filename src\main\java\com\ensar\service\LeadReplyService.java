package com.ensar.service;

import com.ensar.entity.*;
import com.ensar.repository.LeadReplyRepository;
import com.ensar.repository.LeadRepository;
import com.ensar.repository.UserRepository;
import com.ensar.request.dto.CreateUpdateLeadReplyDto;
import com.ensar.response.dto.LeadReplyResponseDto;
import com.ensar.response.dto.LeadResponseResponseDto;
import com.ensar.response.dto.UserResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.sql.Date;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class LeadReplyService {

    @Autowired
    private LeadReplyRepository leadReplyRepository;

    @Autowired
    private LeadRepository leadRepository;

    @Autowired
    private UserRepository userRepository;

    public LeadReply createLeadReply(CreateUpdateLeadReplyDto dto, Organization organization) {
        Lead lead = leadRepository.findById(dto.getLeadId()).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + dto.getLeadId()));
        User replier = userRepository.findById(dto.getReplierId()).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found with id " + dto.getReplierId()));

        LeadReply reply = new LeadReply();
        reply.setLead(lead);
        reply.setReplyText(dto.getReplyText());
        reply.setReplier(replier);
        reply.setReplyAt(dto.getReplyAt());
        reply.setOrganization(organization);

        return leadReplyRepository.save(reply);
    }

    public List<LeadReplyResponseDto> getRepliesForLead(String leadId) {
        return leadReplyRepository.findByLeadId(leadId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    public List<LeadReplyResponseDto> getAllLeadReplies(String orgId) {
        return leadReplyRepository.findByOrganizationId(orgId).stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());
    }

    public Optional<LeadReply> getLeadReplyById(String id) {
        return leadReplyRepository.findById(id);
    }

    public LeadReplyResponseDto convertToDto(LeadReply reply) {
        LeadReplyResponseDto dto = new LeadReplyResponseDto();
        dto.setId(reply.getId());
        dto.setReplyText(reply.getReplyText());
        dto.setLeadId(reply.getLead().getId());
        dto.setReplyAt(reply.getReplyAt());

        if (reply.getReplier() != null) {
            UserResponseDto userDto = new UserResponseDto();
            userDto.setId(reply.getReplier().getId());
            userDto.setFirstName(reply.getReplier().getFirstName());
            userDto.setLastName(reply.getReplier().getLastName());
            userDto.setEmail(reply.getReplier().getEmail());
            dto.setReplier(userDto);
        }

        List<LeadResponseResponseDto> leadResponseResponseDtosDtos = reply.getResponses() != null ?
                reply.getResponses().stream().map(this::convertToDto).collect(Collectors.toList()) : new ArrayList<>();
        dto.setLeadresponses(leadResponseResponseDtosDtos);

        return dto;
    }

    private LeadResponseResponseDto convertToDto(LeadResponse response) {
        LeadResponseResponseDto dto = new LeadResponseResponseDto();
        dto.setId(response.getId());
        dto.setReplyId(response.getReply().getId());
        dto.setResponse(response.getResponse());


        // Convert the replier (User) to UserResponseDto
        LeadReply reply = response.getReply();
        if (reply != null) {
           LeadReplyResponseDto replyDto = new LeadReplyResponseDto();
           replyDto.setId(reply.getId());
           replyDto.setReplyText(reply.getReplyText());
        }

        return dto;
    }


    public void deleteLeadReply(String replyId) {
        leadReplyRepository.deleteById(replyId);
    }

    public LeadReply updateLeadReply(String replyId, CreateUpdateLeadReplyDto dto) {
        LeadReply reply = leadReplyRepository.findById(replyId).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead reply not found with id " + replyId));

        Lead lead = leadRepository.findById(dto.getLeadId()).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Lead not found with id " + dto.getLeadId()));
        User replier = userRepository.findById(dto.getReplierId()).orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "User not found with id " + dto.getReplierId()));

        reply.setLead(lead);
        reply.setReplyText(dto.getReplyText());
        reply.setReplier(replier);
        reply.setReplyAt(dto.getReplyAt());

        return leadReplyRepository.save(reply);
    }

    public List<LeadReplyResponseDto> getLeadRepliesByLeadId(String leadId) {
        List<LeadReply> leadReplies = leadReplyRepository.findByLeadId(leadId);
        return leadReplies.stream().map(this::convertToDto).collect(Collectors.toList());
    }
}

