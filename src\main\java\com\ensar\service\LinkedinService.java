package com.ensar.service;

import com.ensar.entity.Linkedin;
import com.ensar.entity.Organization;
import com.ensar.entity.User;
import com.ensar.repository.LinkedinRepository;
import com.ensar.repository.UserRepository;
import com.ensar.request.dto.CreateUpdateLinkedinDto;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;

import java.io.*;
import java.util.*;

@Service
@Log4j2
@Transactional
public class LinkedinService {

    @Autowired
    private UserRepository userRepository;

    private final LinkedinRepository linkedinRepository;

    @Autowired
    public LinkedinService(LinkedinRepository linkedinRepository) {
        this.linkedinRepository = linkedinRepository;
    }

    public Linkedin getLinkedinById(String id) {
        return linkedinRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Linkedin profile with ID " + id + " not found."));
    }

    public Page<Linkedin> getLinkedinProfilesByPagination(String orgId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return linkedinRepository.findByOrganizationId(orgId,pageable);
    }

    public List<Linkedin> getAllLinkedinProfiles(String orgId) {
        return linkedinRepository.findByOrganizationId(orgId);
    }



    public Linkedin createOrUpdateLinkedin(Optional<String> linkedinId,
                                           CreateUpdateLinkedinDto createUpdateLinkedinDto,
                                           Organization organization) {
        Linkedin linkedin = linkedinId.map(id -> linkedinRepository.findById(id)
                        .orElseThrow(() -> new RuntimeException("Linkedin profile with ID " + id + " not found")))
                .orElseGet(Linkedin::new);

        linkedin.setAccountName(createUpdateLinkedinDto.getAccountName());
        linkedin.setEmail(createUpdateLinkedinDto.getEmail());
        linkedin.setPassword(createUpdateLinkedinDto.getPassword());
        linkedin.setDesignation(createUpdateLinkedinDto.getDesignation());
        linkedin.setCountry(createUpdateLinkedinDto.getCountry());
        linkedin.setConnectionsCount(createUpdateLinkedinDto.getConnectionsCount());
        linkedin.setStatus(createUpdateLinkedinDto.getStatus());
//        linkedin.setHandledBy(createUpdateLinkedinDto.getHandledBy());
        linkedin.setOrganization(organization);

        if (createUpdateLinkedinDto.getHandledById() != null) {
            User user = userRepository.findById(createUpdateLinkedinDto.getHandledById())
                    .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND,
                            "User not found with id " + createUpdateLinkedinDto.getHandledById()));
            linkedin.setHandledBy(user);
        }
        return linkedinRepository.save(linkedin);
    }

    public void deleteLinkedin(String id) {
        linkedinRepository.deleteById(id);
    }

    public void importLinkedinProfiles(InputStream inputStream) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        List<Linkedin> linkedinProfiles = new ArrayList<>();
        Map<String, Integer> headerMap = new HashMap<>();

        if ((line = reader.readLine()) != null) {
            String[] headers = line.split(",");
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
        } else {
            throw new IOException("Empty CSV file");
        }

        while ((line = reader.readLine()) != null) {
            String[] fields = line.split(",");
            if (fields.length != headerMap.size()) {
                throw new IOException("Invalid CSV format. Each line must have the same number of fields as the header.");
            }

            Linkedin linkedin = new Linkedin();
            try {
                linkedin.setAccountName(fields[headerMap.get("account_name")]);
                linkedin.setEmail(fields[headerMap.get("email")]);
                linkedin.setPassword(fields[headerMap.get("password")]);
                linkedin.setDesignation(fields[headerMap.get("designation")]);
                linkedin.setCountry(fields[headerMap.get("country")]);
                linkedin.setConnectionsCount(Integer.parseInt(fields[headerMap.get("connections_count")]));
                linkedin.setStatus(fields[headerMap.get("status")]);
//                linkedin.setHandledBy(fields[headerMap.get("handled_by")]);
            } catch (Exception e) {
                throw new IOException("Error processing line: " + line, e);
            }

            linkedinProfiles.add(linkedin);
        }
        linkedinRepository.saveAll(linkedinProfiles);
    }

    public void exportLinkedinProfiles(OutputStream outputStream) throws IOException {
        List<Linkedin> linkedinProfiles = linkedinRepository.findAll();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("AccountName,Email,Password,Designation,Country,ConnectionsCount,Status,HandledBy\n");
        for (Linkedin linkedin : linkedinProfiles) {
            writer.write(String.format("%s,%s,%s,%s,%s,%d,%s,%s\n",
                    linkedin.getAccountName(),
                    linkedin.getEmail(),
                    linkedin.getPassword(),
                    linkedin.getDesignation(),
                    linkedin.getCountry(),
                    linkedin.getConnectionsCount(),
                    linkedin.getStatus(),
                    linkedin.getHandledBy()));
        }
        writer.flush();
    }
}
