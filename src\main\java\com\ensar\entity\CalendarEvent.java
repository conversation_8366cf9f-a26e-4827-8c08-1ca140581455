package com.ensar.entity;


import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.persistence.*;

import java.sql.Date;
import java.sql.Timestamp;

@Entity(name = "calendar_events")
@Data
@EqualsAndHashCode(callSuper = true)
public class CalendarEvent extends BaseEntity {

    @ManyToOne
    @JoinColumn(name = "user_id",  nullable = false)
    private User user;

    @ManyToOne(fetch = FetchType.LAZY)
//    @JsonBackReference
//    @JsonManagedReference
    @JoinColumn(name = "lead_id", nullable = false)
    private Lead lead;

    @Column(name = "title", nullable = false, length = 100)
    private String title;

    @Column(name = "description", nullable = false, columnDefinition = "TEXT")
    private String description;

    @Column(name = "start_date", nullable = false)
    private Timestamp startDate;

    @Column(name = "end_date", nullable = false)
    private Timestamp endDate;

    @Column(name = "all_day", nullable = false)
    private boolean allDay;

    @Column(name = "color", length = 20)
    private String color;

    @Column(name = "additional_note", nullable = false, columnDefinition = "TEXT")
    private String additionalNote;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;

}


