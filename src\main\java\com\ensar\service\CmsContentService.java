package com.ensar.service;

import com.ensar.entity.CmsContent;
import com.ensar.entity.Organization;
import com.ensar.repository.CmsContentRepository;
import com.ensar.request.dto.CreateUpdateCmsContentDto;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.io.*;
import java.util.*;

@Service
@Log4j2
@Transactional
public class CmsContentService {

    private final CmsContentRepository cmsContentRepository;

    @Autowired
    public CmsContentService(CmsContentRepository cmsContentRepository) {
        this.cmsContentRepository = cmsContentRepository;
    }

    public CmsContent getCmsContentById(String id) {
        Optional<CmsContent> cmsContentOptional = cmsContentRepository.findById(id);
        if (!cmsContentOptional.isPresent())
            throw new RuntimeException("CmsContent with ID " + id + " not found.");

        return cmsContentOptional.get();
    }

    public List<CmsContent> getAllCmsContent(String orgId) {
        return cmsContentRepository.findByOrganizationId(orgId);
    }

    /**
     * Create or update a CmsContent entity.
     */
    public CmsContent createOrUpdateCmsContent(Optional<String> cmsContentId,
                                               CreateUpdateCmsContentDto createUpdateCmsContentDto,
                                               Organization organization) {
        CmsContent cmsContent;
        if (cmsContentId.isPresent()) {
            cmsContent = cmsContentRepository.findById(cmsContentId.get())
                    .orElseThrow(() -> new RuntimeException("CmsContent with ID " + cmsContentId.get() + " not found"));
        } else {
            cmsContent = new CmsContent();
        }

        // Setting fields from DTO
        cmsContent.setTitle(createUpdateCmsContentDto.getTitle());
        cmsContent.setDescription(createUpdateCmsContentDto.getDescription());
        cmsContent.setContent(createUpdateCmsContentDto.getContent());
        cmsContent.setMetaTitle(createUpdateCmsContentDto.getMetaTitle());
        cmsContent.setMetaTags(createUpdateCmsContentDto.getMetaTags());
        cmsContent.setMetaDescription(createUpdateCmsContentDto.getMetaDescription());
        cmsContent.setMetaKeywords(createUpdateCmsContentDto.getMetaKeywords());
        cmsContent.setOrganization(organization);
        // Handling cover image URL and data
//        if (createUpdateCmsContentDto.getCoverUrlData() != null) {
//            saveImageAsBlob(String.valueOf(cmsContent), createUpdateCmsContentDto.getCoverUrlData());
//        } else if (createUpdateCmsContentDto.getCoverUrl() != null) {
//            cmsContent.setCoverUrl(createUpdateCmsContentDto.getCoverUrl());
//        }

        return cmsContentRepository.save(cmsContent);
    }

    public void deleteCmsContent(String id) {
        cmsContentRepository.deleteById(id);
    }

    /**
     * Upload image data as a blob in the database and generate a URL.
     */
    // Inside CmsContentService.java

    public Optional<CmsContent> saveImageAsBlob(String cmsContentId, byte[] imageData) {
            // Set image data and URL
            String baseUrl = ServletUriComponentsBuilder.fromCurrentContextPath().build().toUriString();
            String imageUrl = baseUrl + "/api/cms-contents/" + cmsContentId + "/image";
        return cmsContentRepository.findById(cmsContentId).map(cmsContent -> {

            cmsContent.setCoverUrlData(imageData);  // Save the image data as a byte array
            cmsContent.setCoverUrl(imageUrl);  // Set the image URL

            // Save and return the updated CmsContent wrapped in an Optional
            return cmsContentRepository.save(cmsContent);
        });
    }


    /**
     * Retrieve image data for a given CMS Content ID.
     */
    // Inside CmsContentService.java

    public Optional<CmsContent> getImageData(String cmsContentId) {
        // Retrieve CmsContent by ID and return as Optional
        return cmsContentRepository.findById(cmsContentId);
    }


    public void importCmsContent(InputStream inputStream) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        List<CmsContent> cmsContents = new ArrayList<>();
        Map<String, Integer> headerMap = new HashMap<>();

        if ((line = reader.readLine()) != null) {
            String[] headers = line.split(",");
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
        } else {
            throw new IOException("Empty CSV file");
        }

        while ((line = reader.readLine()) != null) {
            String[] fields = line.split(",");

            if (fields.length != headerMap.size()) {
                throw new IOException("Invalid CSV format. Each line must have the same number of fields as the header.");
            }

            CmsContent cmsContent = new CmsContent();
            try {
                String title = fields[headerMap.get("title")];
                String description = fields[headerMap.get("description")];
                String content = fields[headerMap.get("content")];
                String metaTitle = fields[headerMap.get("meta_title")];
                String metaTags = fields[headerMap.get("meta_tags")];
                String metaDescription = fields[headerMap.get("meta_description")];
                String metaKeywords = fields[headerMap.get("meta_keywords")];

                cmsContent.setTitle(title);
                cmsContent.setDescription(description);
                cmsContent.setContent(content);
                cmsContent.setMetaTitle(metaTitle);
                cmsContent.setMetaTags(metaTags);
                cmsContent.setMetaDescription(metaDescription);
                cmsContent.setMetaKeywords(metaKeywords);
            } catch (Exception e) {
                throw new IOException("Error processing line: " + line, e);
            }

            cmsContents.add(cmsContent);
        }
        cmsContentRepository.saveAll(cmsContents);
    }

    /**
     * Export CMS content data to CSV, including image URL.
     */
    public void exportCmsContent(OutputStream outputStream) throws IOException {
        List<CmsContent> cmsContents = cmsContentRepository.findAll();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("Title,Description,Content,MetaTitle,MetaTags,MetaDescription,MetaKeywords,ImageUrl\n");
        for (CmsContent cmsContent : cmsContents) {
            writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s\n",
                    cmsContent.getTitle(),
                    cmsContent.getDescription(),
                    cmsContent.getContent(),
                    cmsContent.getMetaTitle(),
                    cmsContent.getMetaTags(),
                    cmsContent.getMetaDescription(),
                    cmsContent.getMetaKeywords(),
                    cmsContent.getCoverUrl())); // Image URL included in CSV
        }
        writer.flush();
    }
}

