package com.ensar.controller;

import com.ensar.entity.Organization;
import com.ensar.entity.Role;
import com.ensar.entity.User;
import com.ensar.repository.RoleRepository;
import com.ensar.request.dto.CreateUpdateUserDto;
import com.ensar.security.CurrentUser;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.TotpService;
import com.ensar.service.UserService;

import com.ensar.util.JwtTokenUtil;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.rest.webmvc.ResourceNotFoundException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;


import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "User Mgmt")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/auth")
@Tag(name = "User Management") // Class-level Tag annotation

public class UserController {

    private final UserService userService;
    private final RoleRepository roleRepository;  // Injecting RoleRepository
    private final JwtTokenUtil jwtTokenUtil;
    private final TotpService totpService;

    @Autowired
    public UserController(UserService userService, RoleRepository roleRepository, JwtTokenUtil jwtTokenUtil, TotpService totpService) {  // Add RoleRepository to constructor
        this.userService = userService;
        this.roleRepository = roleRepository;
        this.jwtTokenUtil = jwtTokenUtil;
        this.totpService = totpService;
    }

    @GetMapping("/me")
    public ResponseEntity<Map<String, User>> getLoggedInUser() {
        User user = userService.getLoggedInUser();
        Map<String, User> response = new HashMap<>();
        response.put("user", user);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/email/{email}")
    @PreAuthorize("hasRole('ROLE_SUPER_ADMIN')")
    public ResponseEntity<User> getUserByEmail(@NotBlank @PathVariable("email") String email) {
        User user = userService.getUserByEmail(email);
        if (user == null)
            throw new ResourceNotFoundException();
        return ResponseEntity.ok(user);
    }

    @GetMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Map<String, Page<User>>> getUsers(
//            @RequestParam(name = "orgId", required = false) final String orgId
            @AuthenticationPrincipal EnsarUserDetails userDetails,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size
    ) {
        String orgId = userDetails.getOrganization().getId();
        Page<User> users = userService.getAllAccessibleUsersByPaganization(orgId, page, size);
        Map<String, Page<User>> response = new HashMap<>();
        response.put("users", users);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/all")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Map<String, List<User>>> getAllUsers(
//            @RequestParam(name = "orgId", required = false) final String orgId
            @AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<User> users = userService.getAllAccessibleUsers(orgId);
        Map<String, List<User>> response = new HashMap<>();
        response.put("users", users);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<User> createUser(
            @AuthenticationPrincipal EnsarUserDetails userDetails,
            @Valid @RequestBody CreateUpdateUserDto createUpdateUserDto) {

        Organization organization = userDetails.getOrganization();
        // Set organization ID if it's not provided in the request
//        if (createUpdateUserDto.getOrganizationId() == null) {
//            createUpdateUserDto.setOrganizationId(currentUser.getUser().getOrganization().getId());
//        }

        // Set role ID if it's not provided in the request
        if (createUpdateUserDto.getRoleId() == null) {
            // Fetch the list of roles based on RolePermission. Handle it as a List<Role>.
            List<Role> defaultRoles = roleRepository.findByRolePermission(Role.RolePermission.ROLE_USER);
            if (defaultRoles.isEmpty()) {
                throw new RuntimeException("Default role 'ROLE_USER' not found.");
            }

            // Assume there's only one role for ROLE_USER, and set its ID.
            Role defaultRole = defaultRoles.get(0);
            createUpdateUserDto.setRoleId(defaultRole.getId());
        }

        // Call service to create or update the user
        return ResponseEntity.ok(userService.createOrUpdateUser(Optional.empty(), createUpdateUserDto,organization));
    }


    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<User> updateUser(@PathVariable String id,
                                           @Valid @RequestBody CreateUpdateUserDto createUpdateUserDto,
                                           @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        return ResponseEntity.ok(userService.createOrUpdateUser(Optional.of(id), createUpdateUserDto,organization));
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    @Operation(summary = "Delete a user by ID")
    public ResponseEntity<Void> deleteUser(@PathVariable String id) {
        boolean deleted = userService.deleteUser(id);
        if (deleted) {
            return ResponseEntity.ok().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<User> getUser(@PathVariable String id) {
        return ResponseEntity.ok(userService.getUserById(id));
    }

    @PutMapping("/enable")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Void> enableUsers(@RequestBody List<String> idList) {
        userService.enableOrDisableUsers(idList, false);
        return ResponseEntity.ok().build();
    }

    @PutMapping("/disable")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Void> disableUsers(@RequestBody List<String> idList) {
        userService.enableOrDisableUsers(idList, true);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/enable-2fa")
    public ResponseEntity<String> enable2FA() {
        String userId = userService.getLoggedInUser().getId();
        GoogleAuthenticatorKey secret = userService.generate2FASecret(userId);
        String qrCodeUrl = totpService.generateQrCodeUrl(secret,
                userService.getUserById(userId).getEmail());
        return ResponseEntity.ok(qrCodeUrl);
    }

    @PostMapping("/disable-2fa")
    public ResponseEntity<String> disable2FA() {
        String userId = userService.getLoggedInUser().getId();
        userService.disable2FA(userId);
        return ResponseEntity.ok("2FA disabled");
    }


    @PostMapping("/verify-2fa")
    public ResponseEntity<String> verify2FA(@RequestParam int code) {
        String userId = userService.getLoggedInUser().getId();
        boolean isValid = userService.validate2FACode(userId, code);
        if (isValid) {
            userService.enable2FA(userId);
            return ResponseEntity.ok("2FA Verified");
        } else {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body("Invalid 2FA Code");
        }
    }


    @GetMapping("/user/2fa-status")
    public ResponseEntity<?> get2FAStatus() {
        User user = userService.getLoggedInUser();
        if (user != null){
            return ResponseEntity.ok().body(Map.of("is2faEnabled", user.isTwoFactorEnabled()));
        } else {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body("User not found");
        }
    }


    @PostMapping("/public/verify-2fa-login")
    public ResponseEntity<String> verify2FALogin(@RequestParam int code,
                                                 @RequestParam String jwtToken) {
        String username = jwtTokenUtil.getUsernameFromToken(jwtToken);
        User user = userService.findByUsername(username);
        boolean isValid = userService.validate2FACode(user.getId(), code);
        if (isValid) {
            return ResponseEntity.ok("2FA Verified");
        } else {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body("Invalid 2FA Code");
        }
    }
}
