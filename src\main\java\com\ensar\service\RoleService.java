package com.ensar.service;

import com.ensar.entity.Organization;
import com.ensar.entity.Role;
import com.ensar.repository.RoleRepository;
import com.ensar.request.dto.CreateUpdateRoleDto;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;

@Service
@Log4j2
@Transactional
public class RoleService {

    private final RoleRepository roleRepository;

    @Autowired
    public RoleService(RoleRepository roleRepository) {
        this.roleRepository = roleRepository;
    }

    public Role getRoleById(String id) {
        Optional<Role> roleOptional = roleRepository.findById(id);
        if (!roleOptional.isPresent())
            throw new RuntimeException("Role with ID " + id + " not found.");

        return roleOptional.get();
    }

    public Page<Role> getAllRoles(String orgId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return roleRepository.findByOrganizationId(orgId,pageable);
    }

//    public Role getRoleByName(String roleName) {
//        return roleRepository.findByRoleName(roleName);
//    }


    public Role createOrUpdateRole(Optional<String> roleId,
                                   CreateUpdateRoleDto createUpdateRoleDto,
                                   Organization organization) {
        Role role;
        if (roleId.isPresent()) {
            role = roleRepository.findById(roleId.get())
                    .orElseThrow(() -> new RuntimeException("Role with ID " + roleId.get() + " not found"));
        } else {
            role = new Role();
        }

        role.setRolePermission(Role.RolePermission.valueOf(createUpdateRoleDto.getRolePermission().name()));
        role.setRoleDescription(createUpdateRoleDto.getRoleDescription());
        // Convert from DTO RolePermission to Entity RolePermission
        role.setRoleName(createUpdateRoleDto.getRoleName());
        role.setOrganization(organization);

        return roleRepository.save(role);
    }

    public void deleteRole(String id) {
        roleRepository.deleteById(id);
    }

    public void importRoles(InputStream inputStream) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        List<Role> roles = new ArrayList<>();
        Map<String, Integer> headerMap = new HashMap<>();

        if ((line = reader.readLine()) != null) {
            String[] headers = line.split(",");
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
        } else {
            throw new IOException("Empty CSV file");
        }

        while ((line = reader.readLine()) != null) {
            String[] fields = line.split(",");

            if (fields.length != headerMap.size()) {
                throw new IOException("Invalid CSV format. Each line must have the same number of fields as the header.");
            }

            Role role = new Role();
            try {
//                String rolePermission = fields[headerMap.get("role_permission")];
                Role.RolePermission rolePermission = Role.RolePermission.valueOf(fields[headerMap.get("role_permission")]);
                String roleDescription = fields[headerMap.get("role_description")];
                String roleName = fields[headerMap.get("role_name")];
//                Role.RoleName roleName = Role.RoleName.valueOf(fields[headerMap.get("role_name")]);

                role.setRoleName(roleName);
                role.setRoleDescription(roleDescription);
                role.setRolePermission(rolePermission);
            } catch (Exception e) {
                throw new IOException("Error processing line: " + line, e);
            }

            roles.add(role);
        }
        roleRepository.saveAll(roles);
    }

    public void exportRoles(OutputStream outputStream) throws IOException {
        List<Role> roles = roleRepository.findAll();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("Role Name,Role Description,Role Permission\n");
        for (Role role : roles) {
            writer.write(String.format("%s,%s,%s\n",
                    role.getRolePermission().name(),
                    role.getRoleDescription(),
                    role.getRoleName()));
        }
        writer.flush();
    }
}
