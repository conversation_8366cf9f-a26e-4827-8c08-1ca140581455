plugins {
	id 'java'
	id 'org.springframework.boot' version '3.1.5'
	id 'io.spring.dependency-management' version '1.1.3'
}

group = 'com.ensar'
version = '0.0.1-SNAPSHOT'

java {
	sourceCompatibility = '17'
}

repositories {
	mavenCentral()
}

dependencies {
	 implementation 'org.springframework.boot:spring-boot-starter-actuator'
	 implementation 'org.springframework.boot:spring-boot-devtools'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-data-rest'
    implementation 'org.springframework.boot:spring-boot-starter-hateoas'
    implementation 'org.springframework.boot:spring-boot-starter-mail'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    //implementation 'org.springframework.cloud:spring-cloud-starter-aws-secrets-manager-config:2.2.4.RELEASE'
    implementation 'org.springframework.session:spring-session-core'
    runtimeOnly 'mysql:mysql-connector-java:8.0.33'
    // AWS SDK for Java (SQS)
    implementation platform('software.amazon.awssdk:bom:2.20.40') // Ensure this version is correct for your setup
    implementation 'software.amazon.awssdk:sqs'

// Spring Cloud AWS (for SQS integration)
    implementation 'io.awspring.cloud:spring-cloud-aws-messaging:2.4.3'

// Spring Boot Starter for Spring Cloud
    implementation 'org.springframework.cloud:spring-cloud-starter-aws:2.2.6.RELEASE'

    implementation 'org.flywaydb:flyway-core:9.22.3'
    implementation 'org.flywaydb:flyway-mysql:9.22.3'

    // Apache POI
    implementation 'org.apache.poi:poi-ooxml:5.2.3'


    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.6.0'
    implementation 'org.projectlombok:lombok:1.18.20'
    annotationProcessor 'org.projectlombok:lombok:1.18.20'

//    implementation 'io.springfox:springfox-boot-starter:3.0.0'
//    implementation 'io.springfox:springfox-swagger-ui:2.9.2'

       implementation 'io.jsonwebtoken:jjwt-api:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-impl:0.11.5'
    runtimeOnly 'io.jsonwebtoken:jjwt-jackson:0.11.5'

    //implementation 'com.amazonaws:aws-java-sdk:1.12.68'
    implementation 'com.amazonaws:aws-java-sdk-quicksight:1.12.84'
    implementation 'com.amazonaws:aws-java-sdk-secretsmanager:1.12.84'
    
    implementation 'com.opencsv:opencsv:5.9'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'com.warrenstrange:googleauth:1.4.0'
    
    
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
}

tasks.named('bootBuildImage') {
	builder = 'paketobuildpacks/builder-jammy-base:latest'
}

tasks.named('test') {
	useJUnitPlatform()
}
