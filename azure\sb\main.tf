provider "azurerm" {
  features {}
  subscription_id = "6618dfa1-6cfd-42ef-9fca-537b7512fd17"  # Directly specifying here
}

# Define the values directly
locals {
  resource_group_name       = "ai2"
  app_service_name          = "ensar-crm-h-api"
  location                  = "eastus"
  hosting_plan_name         = "crm-api-app-plan"
  server_farm_resource_group = "ai2"
  always_on                 = true
  ftps_state                = "Disabled"
  linux_fx_version          = "JAVA|17"
}

# Data source for the existing resource group
data "azurerm_resource_group" "existing" {
  name = local.resource_group_name
}

# App Service Plan
resource "azurerm_service_plan" "asp" {
  name                = local.hosting_plan_name
  location            = local.location
  resource_group_name = local.server_farm_resource_group
  os_type             = "Linux"
  sku_name            = "B3"
}


# App Service
resource "azurerm_linux_web_app" "app" {
  name                = local.app_service_name
  location            = data.azurerm_resource_group.existing.location
  resource_group_name = data.azurerm_resource_group.existing.name
  service_plan_id     = azurerm_service_plan.asp.id
  public_network_access_enabled = true
  site_config {
    always_on        = local.always_on
    ftps_state       = local.ftps_state
    application_stack {
      java_server         = "JAVA"
      java_version        = "17"
      java_server_version = "17"
    }
  }

  app_settings = {
  "ApplicationInsightsAgent_EXTENSION_VERSION" = "~3"
  "XDT_MicrosoftApplicationInsights_Mode"      = "default"
  "SCM_DO_BUILD_DURING_DEPLOYMENT"             = "true"
  "WEBSITE_RUN_FROM_PACKAGE"                   = "1"
  "SERVER_PORT"                                = "80"
  "JWT_SECRET_KEY"                             = "fWnJveXKFm0c/tll7gUOwXfXb4j4CbCvuYeYDEGO02M="
  "JWT_TTL_MINS"                               = "180"
  "DB_SERVER"                                  = "ensarmysql4.mysql.database.azure.com"
  "DB_PORT"                                    = "3306"
  "DB_SCHEMA"                                  = "crm"
  "DB_USE_SSL"                                 = "true"
  "DB_REQUIRE_SSL"                             = "true"
  "DB_USER"                                    = "ensaradmin"
  "DB_PASSWORD"                    = "YourSecurePasswordHere!"
  "EMAIL_FROM"                                 = "<EMAIL>"
  "EMAIL_HOST"                                 = "smtp.gmail.com"
  "EMAIL_USER_NAME"                            = "amzadshaik770"
  "EMAIL_USER_PWD"                             = "cnvnnvdfxihxjvbf"
  "AWS_ACCESS_KEY"                             = "********************"
  "AWS_SECRET_KEY"                             = "8knQ0EbCebTrPttnuYKr/cL7zqkegYQiecw+/j6+"
  "AWS_ACCOUNT_ID"                             = "************"
  "cloud.aws.credentials.accessKey"           = "********************"
  "cloud.aws.credentials.secretKey"           = "8knQ0EbCebTrPttnuYKr/cL7zqkegYQiecw+/j6+"
  "cloud.aws.region.static"                    = "us-east-1"
  "cloud.aws.sqs.queue.url"                    = "https://sqs.us-east-1.amazonaws.com/your-account-id/Crm_Mail.fifo"
  "SENDGRID_API_KEY"                           = "*********************************************************************"
  "spring.servlet.multipart.enabled"           = "true"
  "spring.mail.host"                           = "smtp.gmail.com"
  "spring.mail.port"                           = "587"
  "spring.mail.username"                       = "<EMAIL>"
  "spring.mail.password"                       = "cnvnnvdfxihxjvbf"
  "spring.mail.properties.mail.smtp.starttls.enable"   = "true"
  "spring.mail.properties.mail.smtp.starttls.required" = "true"
  "spring.mail.properties.mail.smtp.auth"      = "true"
 
  "sqs.enabled"                                = "true"
  "mail.smtp.connectiontimeout"                = "10000"
  "mail.smtp.timeout"                          = "10000"
  }

  https_only = true
  client_affinity_enabled = false
 
}

# GitHub Source Control
resource "azurerm_app_service_source_control" "github" {
  app_id                 = azurerm_linux_web_app.app.id
  repo_url               = "https://github.com/nproducts2/crm-api"
  branch                 = "main"
  use_manual_integration = false
  use_mercurial          = false

  github_action_configuration {
    generate_workflow_file = true
    code_configuration {
      runtime_stack   = "spring"
      runtime_version = "17"
    }
  }
}

# Create a GitHub Personal Access Token (PAT) secret
resource "azurerm_app_service_source_control_token" "github_token" {
  type  = "GitHub"
  token = "****************************************"
}