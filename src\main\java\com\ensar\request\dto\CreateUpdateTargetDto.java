package com.ensar.request.dto;

import com.ensar.entity.Target;
import com.ensar.entity.User;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

import java.sql.Date;

@Schema(description = "Parameters required to create/update target")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateTargetDto {

    @Schema(description = "Account Name", required = true)
    @NotBlank(message = "Account Name is required")
    @Size(max = 100)
    private String accountName;

    @Schema(description = "Connections Count", required = true)
    @NotNull(message = "Connections Count is required")
    @Min(value = 0, message = "Connections Count must be greater than or equal to 0")
    private Integer connectionsCount;

    @Schema(description = "Handled By", required = true)
//    @NotBlank(message = "Handled By is required")
//    @Size(max = 100)
    private String handledById;

    @Schema(description = "No. of Leads Identified", required = true)
    @NotNull(message = "No. of Leads Identified is required")
    @Min(value = 0, message = "No. of Leads Identified must be greater than or equal to 0")
    private Integer noOfLeadsIdentified;

    @Schema(description = "Connections Sent", required = true)
    @NotNull(message = "Connections Sent is required")
    @Min(value = 0, message = "Connections Sent must be greater than or equal to 0")
    private Integer connectionsSent;

    @Schema(description = "Messages Sent", required = true)
    @NotNull(message = "Messages Sent is required")
    @Min(value = 0, message = "Messages Sent must be greater than or equal to 0")
    private Integer messagesSent;

    @Schema(description = "Follow Ups", required = true)
    @NotNull(message = "Follow Ups is required")
    @Min(value = 0, message = "Follow Ups must be greater than or equal to 0")
    private Integer followUps;

    @Schema(description = "Response Received", required = true)
    @NotNull(message = "Response Received is required")
    private String responseReceived;

    @Schema(description = "Meetings Scheduled", required = true)
    @NotNull(message = "Meetings Scheduled is required")
    private Integer meetingsScheduled;

    @Schema(description = "In Mail Count", required = true)
    @NotNull(message = "In Mail Count is required")
    @Min(value = 0, message = "In Mail Count must be greater than or equal to 0")
    private Integer inMailCount;

    @Schema(description = "Postings", required = true)
    @NotNull(message = "Postings is required")
    @Min(value = 0, message = "Postings must be greater than or equal to 0")
    private Integer postings;

    @Schema(description = "Created date is required", required = true)
    @NotNull(message = "Created date is required")
    private Date createdDate;

    @Schema(description ="Status", required = true)
    @NotNull(message = "Status is required")
    private Target.Status status;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;


}
