package com.ensar.response.dto;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class UserResponseDto {

    private String id;
    private String firstName;
    private String lastName;
    private String email;
    private String city;
    private String state;
    private String country;
    private String address;
    private String zipCode;
    private String phoneNumber;
    private String company;
    private String avatarUrl;
    private String status;  // Account status (e.g., active, disabled)
    private boolean verified;  // Account verification status (fixed capitalization)
    private boolean emailVerified;  // Email verification status
    private String roleId;  // Role ID of the user
    private String organizationId;  // Organization ID
}
