# import pandas as pd
# import mysql.connector
# from mysql.connector import Error
# from datetime import datetime
# import re

# # Step 1: Read the Excel file
# excel_file = "leads.xlsx"
# df = pd.read_excel(excel_file)

# # Step 2: Strip spaces from column names
# df.columns = df.columns.str.strip()

# # Step 3: Convert 'Sent Date' column to datetime if it's not already
# df['Sent Date'] = pd.to_datetime(df['Sent Date'], errors='coerce')

# # Step 4: Establish a connection to the MySQL database
# try:
#     connection = mysql.connector.connect(
#         host='localhost',
#         database='crm',
#         user='root',
#         password='root'
#     )

#     error_count = 0

#     duplicate_count=0

#     if connection.is_connected():
#         cursor = connection.cursor()
        
#         def remove_emojis_and_specials(text):
#             if isinstance(text, str):
#                 # This removes emojis and special characters, keeps only letters, digits, and space
#                 return re.sub(r'[^\w\s]', '', text)
#             return ''  # If it's not a string, return as-is

#         def remove_emojis(name):
#             if isinstance(name, str):
#                 # Regular expression to remove emojis from the text
#                 # This regex targets characters in the Unicode ranges commonly used for emojis
#                 return re.sub(r'[\U00010000-\U0010ffff\U00002000-\U00002b50\U00002764\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F700-\U0001F77F\U0001F780-\U0001F7FF\U0001F800-\U0001F8FF\U0001F900-\U0001F9FF\U0001FA00-\U0001FA6F\U0001FA70-\U0001FAFF\U00002702-\U000027B0\U0001F004-\U0001F0CF]', '', name)
#             return name  # If it's not a string, return as-is
        
#         print("Records insreting into table...")

#         # Step 5: Iterate over each row in the DataFrame and insert into the database
#         for index, row in df.iterrows():
#             try:

#                 raw_name = row['Name']
#                 clean_name = remove_emojis_and_specials(raw_name).strip()

#                 if clean_name:
#                     name_parts = clean_name.split()
#                     first_name = name_parts[0] if len(name_parts) > 0 else ''
#                     last_name = name_parts[1] if len(name_parts) > 1 else ''
#                 else:
#                     first_name = ''
#                     last_name = ''

#                 email = row['Mail ID']
#                 phonenumber = row['Mobile Number']
#                 # lead_date = row['Sent Date'] if pd.notna(row['Sent Date']) else datetime.now()  # Use current datetime if NaN
#                 lead_date = row['Sent Date'] if pd.notna(row['Sent Date']) and row['Sent Date'] != '1900-08-09' else None
                
              
#                 if lead_date is not None:
#                     lead_date_str = lead_date.strftime('%Y-%m-%d %H:%M:%S')
#                 else:
#                     lead_date_str = None

#                 organization_id = None
#                 status = 'New'  # Assuming new by default
#                 linkedin = remove_emojis(row['Lead link'])  # Remove emojis from linkedin
#                 website = remove_emojis(row['Website'])  # Remove emojis from website
#                 region = row['State']
#                 empcount = row['Company headcount']
#                 comments = ''  # You can modify this based on the available column in your file
#                 industry_id = None  # Default value before lookup
#                 designation_id = None  # Default value before lookup
#                 # sentby = row['Sent By']
#                 sentby = str(row['Sent By']) if pd.notna(row['Sent By']) else None
#                 messagesent = 1 if str(row['sent']).strip() == 'Done' else 0

#                 organization_query = """
#                     SELECT id
#                     FROM organization
#                     WHERE name = 'Demo Org'
#                     LIMIT 1
#                 """
#                 cursor.execute(organization_query)
#                 organization_result = cursor.fetchone()

#                 # Ensure we are unpacking the result from the tuple returned by fetchone()
#                 if organization_result:
#                     # If "Demo Org" exists, use that organization id
#                     organization_id = organization_result[0]
#                 else:
#                     # If "Demo Org" does not exist, insert it and then get the ID
#                     insert_org_query = """
#                         INSERT INTO organization (name) 
#                         VALUES ('Demo Org')
#                     """
#                     cursor.execute(insert_org_query)
#                     connection.commit()  # Commit the insert
#                     cursor.execute(organization_query)  # Fetch the new organization_id
#                     organization_result = cursor.fetchone()
#                     organization_id = organization_result[0]

#                 # Check if email, phone number or linkedin already exists
#                 check_existing_query = """
#                     SELECT id FROM leads
#                     WHERE email = %s OR phonenumber = %s OR linkedin = %s
#                     LIMIT 1
#                 """
#                 cursor.execute(check_existing_query, (email, phonenumber, linkedin))
#                 existing_record = cursor.fetchone()

#                 if existing_record:
#                     duplicate_count +=1
#                     # print(f"Record already exists for Email: {email}, Phone: {phonenumber}, LinkedIn: {linkedin}. Skipping...")
#                     continue  # Skip this record if any value already exists

#                 # SQL Query to get industry_id for "IT"
#                 industry_query = """
#                     SELECT id
#                     FROM industries
#                     WHERE name LIKE %s
#                     LIMIT 1
#                 """
#                 cursor.execute(industry_query, ('%IT%',))
#                 industry_result = cursor.fetchone()
           

#                 # SQL Query to check if the designation exists
#                 designation_query = """
#                     SELECT id
#                     FROM designations
#                     WHERE name = %s
#                     LIMIT 1
#                 """
#                 cursor.execute(designation_query, (row['Occupation'],))
#                 designation_result = cursor.fetchone()

#                 if designation_result:
#                     # If the designation exists, use the corresponding id
#                     designation_id = designation_result[0]
#                 elif pd.notna(row['Occupation']) and row['Occupation'].strip() != "":
#                     insert_designation_query = """
#                         INSERT INTO designations (id, name,organization_id)
#                         VALUES (UUID(), %s, %s)
#                     """
#                     cursor.execute(insert_designation_query, (row['Occupation'],organization_id,))
#                     connection.commit()  # Commit the insert
#                     cursor.execute(designation_query, (row['Occupation'],))  # Fetch the new designation_id
#                     designation_result = cursor.fetchone()
#                     designation_id = designation_result[0] if designation_result else None         
#                 else:
#                     # If designation does not exist, fallback to "Business Analyst"
#                     fallback_designation_query = """
#                         SELECT id
#                         FROM designations
#                         WHERE name = 'Business Analyst'
#                         LIMIT 1
#                     """
#                     cursor.execute(fallback_designation_query)
#                     fallback_designation_result = cursor.fetchone()
#                     designation_id = fallback_designation_result[0] if fallback_designation_result else None


#                 if sentby:
#                     # Split the name into first and last name (assumes format: "First Last")
#                     name_parts = sentby.strip().split(" ")
#                     fname = name_parts[0]
#                     lname = name_parts[1] if len(name_parts) > 1 else "User"

#                     # Step 1: Check if user exists with the same first_name and last_name
#                     user_query = "SELECT id FROM user WHERE first_name = %s LIMIT 1"
#                     cursor.execute(user_query, (fname,))
#                     user_result = cursor.fetchone()

#                     if user_result:
#                         sentby = user_result[0]
#                     else:
#                         # Step 2: Get role_id where role_permission = 'ROLE_USER'
#                         role_query = "SELECT id FROM role WHERE role_permission = 'ROLE_USER' LIMIT 1"
#                         cursor.execute(role_query)
#                         role_result = cursor.fetchone()

#                         if not role_result:
#                             raise Exception("ROLE_USER not found in role_permission table")
                        
#                         role_id = role_result[0]

#                         generated_email = (sentby).lower() + "@ensarsolutions.com"

#                         insert_user_query = """
#                             INSERT INTO user (
#                                 id, first_name, last_name, email,
#                                 role_id, organization_id
#                             ) VALUES (
#                                 UUID(), %s, %s, %s,
#                                 %s, %s
#                             )
#                         """
#                         cursor.execute(insert_user_query, (
#                             fname, lname, generated_email,
#                             role_id, organization_id
#                         ))
#                         connection.commit()

#                         # Step 5: Fetch newly created user's ID
#                         cursor.execute(user_query, (fname,))
#                         user_result = cursor.fetchone()
#                         sentby = user_result[0]
#                 else:
#                     # Fallback: Sent By is null → use <EMAIL>
#                     fallback_query = "SELECT id FROM user WHERE email = %s LIMIT 1"
#                     cursor.execute(fallback_query, ("<EMAIL>",))
#                     fallback_result = cursor.fetchone()

#                     if fallback_result:
#                         sentby = fallback_result[0]
#                     else:
#                         raise Exception("Fallback user <NAME_EMAIL> not found")


#                 # SQL INSERT query
#                 insert_query = """
#                     INSERT INTO `leads` (
#                         `id`, `organization_id`, `designation_id`, `email`, `lead_date`, `phonenumber`, `status`,
#                         `linkedin`, `website`, `region`, `empcount`, `verified`, `messagesent`, `comments`,
#                         `sentby`, `created_date_time`, `last_updated_date_time`, `industry_id`,
#                         `first_name`, `last_name`, `draft_status`
#                     )
#                     VALUES (
#                         UUID(), %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, %s, %s, %s, NOW(), NOW(), %s, %s, %s, 0
#                     )
#                 """

#                 # Execute the query
#                 cursor.execute(insert_query, (
#                     organization_id, designation_id, email, lead_date_str, phonenumber, status, linkedin, website,
#                     region, empcount, messagesent, comments, sentby, industry_id, first_name, last_name
#                 ))

#                 # Commit the transaction
#                 connection.commit()

#             except Error as e:
#                 error_count += 1
#                 # Print error along with the S. No (or index) of the row
#                 print(f"Error at S. No {row.get('S No', index + 1)}: {e}")
#                 # print(f"Error fname {first_name} last_name {last_name}")
#                 continue  # Continue to the next record on error

#         print(f"{duplicate_count} Duplicate records are skipped.")
#         print(f"{len(df)-duplicate_count-error_count} records inserted successfully.")

# except Error as e:
#     print(f"Error while connecting to MySQL: {e}")
# finally:
#     # Close the database connection
#     if connection.is_connected():
#         cursor.close()
#         connection.close()


import pandas as pd
import mysql.connector
from mysql.connector import Error
from datetime import datetime
import re
from openpyxl import load_workbook

# Step 1: Read the Excel file using openpyxl
excel_file = "leads.xlsx"
df = pd.read_excel(excel_file, engine='openpyxl')

# Step 2: Strip spaces from column names
df.columns = df.columns.str.strip()

# Step 3: Extract actual hyperlinks from "Lead link" and "Website" columns
workbook = load_workbook(excel_file, data_only=True)
sheet = workbook.active

lead_link_col = None
website_col = None

# Identify columns for Lead link and Website
for col in sheet.iter_cols(1, sheet.max_column):
    header = col[0].value
    if header:
        if header.strip() == "Lead link":
            lead_link_col = col
        elif header.strip() == "Website":
            website_col = col

# Replace displayed names with actual hyperlinks
for idx in range(2, sheet.max_row + 1):  # Start from row 2 (row 1 = header)
    if lead_link_col and lead_link_col[idx - 1].hyperlink:
        df.at[idx - 2, 'Lead link'] = lead_link_col[idx - 1].hyperlink.target
    if website_col and website_col[idx - 1].hyperlink:
        df.at[idx - 2, 'Website'] = website_col[idx - 1].hyperlink.target

# Step 4: Convert 'Sent Date' column to datetime
df['Sent Date'] = pd.to_datetime(df['Sent Date'], errors='coerce')

# Step 5: Establish DB connection
try:
    connection = mysql.connector.connect(
        host='localhost',
        database='crm',
        user='root',
        password='root'
    )

    error_count = 0
    duplicate_count = 0

    if connection.is_connected():
        cursor = connection.cursor()

        # def remove_emojis_and_specials(text):
        #     if isinstance(text, str):
        #         return re.sub(r'[^\w\s]', '', text)
        #     return ''

        def remove_emojis(name):
            if isinstance(name, str):
                return re.sub(r'[\U00010000-\U0010ffff\U00002000-\U00002b50\U00002764\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F700-\U0001F77F\U0001F780-\U0001F7FF\U0001F800-\U0001F8FF\U0001F900-\U0001F9FF\U0001FA00-\U0001FA6F\U0001FA70-\U0001FAFF\U00002702-\U000027B0\U0001F004-\U0001F0CF]', '', name)
            return name

        print("Records inserting into table...")

        for index, row in df.iterrows():
            try:
                raw_name = row['Name']

                if isinstance(raw_name, str):
                    clean_name = remove_emojis(raw_name).strip()
                else:
                    # If raw_name is a float, convert it to a string before processing
                    clean_name = remove_emojis(str(raw_name)).strip()

                if clean_name:
                    # Regex to capture text between parentheses and words (handling names like (Jay))
                    name_parts = re.findall(r'\S+|\([^\)]+\)', clean_name)
                    
                    first_name = name_parts[0]
                    
                    # Join remaining parts after the first word to form last_name
                    last_name = ' '.join(name_parts[1:]) if len(name_parts) > 1 else ''
                    
                    # Ensure the last_name doesn't exceed 50 characters
                    if len(last_name) > 50:
                        words = last_name.split()
                        while len(' '.join(words)) > 50:  # While the length exceeds 50 characters
                            words.pop()  # Remove the last word to make the string shorter
                        last_name = ' '.join(words)
                else:
                    first_name = ''
                    last_name = ''

                email = row['Mail ID']
                phonenumber = row['Mobile Number']
                # lead_date = row['Sent Date'] if pd.notna(row['Sent Date']) and str(row['Sent Date']) != '1900-08-09' else None
                # lead_date_str = lead_date.strftime('%Y-%m-%d %H:%M:%S') if lead_date else None
                lead_date = row['Sent Date'] if pd.notna(row['Sent Date']) and row['Sent Date'] != '' else None

                # If the date is still invalid, replace it with None
                if isinstance(lead_date, str) and not pd.to_datetime(lead_date, errors='coerce'):
                    lead_date = None

                lead_date_str = lead_date.strftime('%Y-%m-%d %H:%M:%S') if lead_date else None

                status = 'New'
                linkedin = remove_emojis(row['Lead link'])
                website = remove_emojis(row['Website'])
                region = row['State']
                empcount = str(row['Company headcount']).replace(" to ", "-").strip() if pd.notna(row['Company headcount']) else None
                comments = ''
                industry_id = None
                designation_id = None
                sentby = str(row['Sent By']) if pd.notna(row['Sent By']) else None
                messagesent = 1 if str(row['sent']).strip() == 'Done' else 0

                organization_query = "SELECT id FROM organization WHERE name = 'Demo Org' LIMIT 1"
                cursor.execute(organization_query)
                organization_result = cursor.fetchone()

                if organization_result:
                    organization_id = organization_result[0]
                else:
                    cursor.execute("INSERT INTO organization (name) VALUES ('Demo Org')")
                    connection.commit()
                    cursor.execute(organization_query)
                    organization_id = cursor.fetchone()[0]

                check_existing_query = """
                    SELECT id FROM leads
                    WHERE email = %s OR phonenumber = %s OR linkedin = %s
                    LIMIT 1
                """
                cursor.execute(check_existing_query, (email, phonenumber, linkedin))
                if cursor.fetchone():
                    duplicate_count += 1
                    continue

                industry_query = "SELECT id FROM industries WHERE name LIKE %s LIMIT 1"
                cursor.execute(industry_query, ('%IT%',))
                industry_result = cursor.fetchone()
                if industry_result:
                    industry_id = industry_result[0]

                designation_query = "SELECT id FROM designations WHERE name = %s LIMIT 1"
                cursor.execute(designation_query, (row['Occupation'],))
                designation_result = cursor.fetchone()

                if designation_result:
                    designation_id = designation_result[0]
                elif pd.notna(row['Occupation']) and row['Occupation'].strip():
                    cursor.execute("INSERT INTO designations (id, name, organization_id) VALUES (UUID(), %s, %s)",
                                   (row['Occupation'], organization_id))
                    connection.commit()
                    cursor.execute(designation_query, (row['Occupation'],))
                    designation_id = cursor.fetchone()[0]
                else:
                    cursor.execute("SELECT id FROM designations WHERE name = 'Business Analyst' LIMIT 1")
                    designation_id = cursor.fetchone()[0]

                if sentby:
                    name_parts = sentby.strip().split(" ")
                    fname = name_parts[0]
                    lname = name_parts[1] if len(name_parts) > 1 else "User"

                    user_query = "SELECT id FROM user WHERE first_name = %s LIMIT 1"
                    cursor.execute(user_query, (fname,))
                    user_result = cursor.fetchone()

                    if user_result:
                        sentby = user_result[0]
                    else:
                        cursor.execute("SELECT id FROM role WHERE role_permission = 'ROLE_USER' LIMIT 1")
                        role_id = cursor.fetchone()[0]
                        generated_email = (sentby).lower().replace(" ", "") + "@ensarsolutions.com"

                        cursor.execute("""
                            INSERT INTO user (id, first_name, last_name, email, role_id, organization_id)
                            VALUES (UUID(), %s, %s, %s, %s, %s)
                        """, (fname, lname, generated_email, role_id, organization_id))
                        connection.commit()
                        cursor.execute(user_query, (fname,))
                        sentby = cursor.fetchone()[0]
                else:
                    cursor.execute("SELECT id FROM user WHERE email = %s LIMIT 1", ("<EMAIL>",))
                    sentby = cursor.fetchone()[0]

                insert_query = """
                    INSERT INTO `leads` (
                        `id`, `organization_id`, `designation_id`, `email`, `lead_date`, `phonenumber`, `status`,
                        `linkedin`, `website`, `region`, `empcount`, `verified`, `messagesent`, `comments`,
                        `sentby`, `created_date_time`, `industry_id`,
                        `first_name`, `last_name`, `draft_status`
                    )
                    VALUES (
                        UUID(), %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, %s, %s, %s, NOW(), %s, %s, %s, 0
                    )
                """
                cursor.execute(insert_query, (
                    organization_id, designation_id, email, lead_date_str, phonenumber, status, linkedin, website,
                    region, empcount, messagesent, comments, sentby, industry_id, first_name, last_name
                ))
                connection.commit()

            except Error as e:
                error_count += 1
                print(f"Error at S. No {row.get('S No', index + 1)}: {e}")
                continue

        print(f"{duplicate_count} Duplicate records are skipped.")
        print(f"{len(df)-duplicate_count-error_count} records inserted successfully.")

except Error as e:
    print(f"Error while connecting to MySQL: {e}")
finally:
    if connection.is_connected():
        cursor.close()
        connection.close()
