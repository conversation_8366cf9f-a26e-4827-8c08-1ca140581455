import pandas as pd
import mysql.connector
from mysql.connector import Error
import uuid

# Load Excel file with industry data
file_path = 'June Email - 2024.xlsx'
excel_data = pd.ExcelFile(file_path)

# MySQL database connection details
host = 'localhost'
user = 'root'
password = 'root'
database = 'crm'

try:
    connection = mysql.connector.connect(
        host=host,
        user=user,
        password=password,
        database=database
    )
    if connection.is_connected():
        cursor = connection.cursor()

        # Fetch existing industries in the database to prevent duplicates
        cursor.execute("SELECT name, id FROM industries")
        existing_industries = {name: ind_id for name, ind_id in cursor.fetchall()}

        # Insert new industries and map industry names to IDs
        industry_name_to_id = {}
        for sheet_name in excel_data.sheet_names:
            # Load data from each sheet and standardize column names
            df = pd.read_excel(file_path, sheet_name=sheet_name)
            df.columns = [column.lower().replace(" ", "_") for column in df.columns]

            # Ensure industry column exists
            if 'industry' not in df.columns:
                print(f"Warning: 'industry' column not found in sheet '{sheet_name}'. Skipping.")
                continue
            
            # Insert unique industries from the industry column into the industries table
            for industry_name in df['industry'].dropna().unique():
                industry_name = industry_name[:50]  # Truncate to fit VARCHAR(50)
                if industry_name not in existing_industries:
                    industry_id = str(uuid.uuid4())
                    cursor.execute(
                        """
                        INSERT INTO industries (id, name) 
                        VALUES (%s, %s)
                        """, (industry_id, industry_name)
                    )
                    existing_industries[industry_name] = industry_id
                industry_name_to_id[industry_name] = existing_industries[industry_name]

        # Commit new industry inserts
        connection.commit()

        
        print("Industries inserted and leads updated successfully based on industry column in each sheet.")

except Error as e:
    print("Error while connecting to MySQL:", e)
finally:
    if connection.is_connected():
        cursor.close()
        connection.close()
        print("MySQL connection is closed")
