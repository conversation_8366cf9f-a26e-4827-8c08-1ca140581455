package com.ensar.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.persistence.*;

import java.math.BigDecimal;
import java.sql.Date;

@Entity(name = "deals")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class Deal extends BaseEntity {


    @Column(name = "name", nullable = false, length = 255)
    private String name;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "lead_id", nullable = false)
    private Lead leads;

    @Column(name = "email", nullable = false, length = 255)
    private String email;

    @Enumerated(EnumType.STRING)
    @Column(name = "stage", nullable = false, length = 100)
    private Stage stage;

    public enum Stage {
        PROSPECTING,
        NEW,
        NEGOTIATION,
        PROPOSAL,
        CLOSED_WON,
        CLOSED_LOST
    }

    @Column(name = "value", nullable = false, precision = 15, scale = 2)
    private BigDecimal value;

    @Column(name = "expected_close_date", nullable = false)
    private Date expectedCloseDate;

    @Column(name = "actual_close_date")
    private Date actualCloseDate;


    @Column(name = "status", nullable = false)
    @Enumerated(EnumType.STRING)
    private Status status;

    public enum Status {
        ACTIVE,
        INACTIVE,
        CLOSED
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "priority", nullable = false, length = 50)
    private Priority priority;

    public enum Priority {
        LOW,
        MEDIUM,
        HIGH
    }

    @Enumerated(EnumType.STRING)
    @Column(name = "source", length = 100)
    private Source source;

    public enum Source {
        REFERRAL,
        WEBSITE,
        COLD_CALL
    }

    @Column(name = "next_step", length = 255)
    private String nextStep;

    @Column(name = "notes")
    private String notes;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;

    // Additional fields (created_at, updated_at) are assumed to be handled by BaseEntity
}

