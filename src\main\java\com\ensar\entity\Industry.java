package com.ensar.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@Entity(name = "industries")
//@JsonIgnoreProperties({"hibernateLazyInitializer", "handler"}) 
@JsonIgnoreProperties(value = {"industries", "hibernateLazyInitializer"})
@Data
@EqualsAndHashCode(callSuper = true)
public class Industry extends BaseEntity {

    @Column(name = "name", nullable = false, length = 50)
    private String name;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;
    
}
