DROP TABLE IF EXISTS `industries`;

CREATE TABLE industries
(
    id                    CHAR(36)    NOT NULL,
    name                  VA<PERSON><PERSON><PERSON>(50) NOT NULL,
    organization_id CHAR(36) NOT NULL,
    `created_date_time`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp NULL     DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_industries_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB
DEFAULT CHARSET = utf8;