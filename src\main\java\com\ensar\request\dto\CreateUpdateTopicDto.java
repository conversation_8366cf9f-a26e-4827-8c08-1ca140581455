package com.ensar.request.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Schema(description = "Parameters required to create/update a Topic")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateTopicDto {

    @Schema(description = "Topic Name", required = true)
    @NotBlank(message = "Topic Name is required")
    @Size(max = 100)
    private String name;

    @Schema(description = "Topic Category", required = true)
    @NotBlank(message = "Topic Category is required")
    @Size(max = 100)
    private String category;

    @Schema(description =  "Topic Level", required = true)
    @NotBlank(message = "Topic Level is required")
    @Pattern(regexp = "HIGH|MEDIUM|LOW", message = "Invalid Topic Level. Allowed values are HIGH, MEDIUM, LOW")
    private String level;

    @Schema(description = "Topic ID", required = true)
    @NotBlank(message = "Topic ID is required")
    //@Pattern(regexp = Constants.UUID_PATTERN, message = "Invalid Topic ID")
    private String topicId;

}
