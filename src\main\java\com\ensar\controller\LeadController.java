package com.ensar.controller;

import com.ensar.config.TenantContext;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateLeadDto;
import com.ensar.response.dto.LeadResponseDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.LeadService;

import io.swagger.v3.oas.annotations.tags.Tag;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import jakarta.servlet.http.HttpServletResponse;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "Lead Mgmt")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/leads")
@Tag(name = "Lead Management") // Class-level Tag annotation
@Slf4j
public class LeadController {

    private final LeadService leadService;

    @Autowired
    public LeadController(LeadService leadService) {
        this.leadService = leadService;
    }

    @GetMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<LeadResponseDto> getLeadById(@PathVariable String id) {
        String organizationId = TenantContext.getCurrentTenant(); // Retrieve the current organization ID
        log.info("organizationId"+organizationId);
        
        Optional<LeadResponseDto> lead = leadService.getLeadById(id);
        return lead.map(ResponseEntity::ok)
                .orElseThrow(() -> new ResourceNotFoundException("Lead with id " + id + " not found."));
    }

//    @GetMapping("/")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
//    public ResponseEntity<Map<String, Page<LeadResponseDto>>> getLeadsByPagination(@AuthenticationPrincipal EnsarUserDetails userDetails,
//                                                                          @RequestParam(defaultValue = "0") int page,
//                                                                          @RequestParam(defaultValue = "10") int size) {
//        String orgId = userDetails.getOrganization().getId();
//        String organizationId = TenantContext.getCurrentTenant(); // Retrieve the current organization ID
//        log.info("organizationId"+organizationId);
//        Page<LeadResponseDto> leads = leadService.getLeadsByPagination(orgId, page ,size);
//        Map<String, Page<LeadResponseDto>> response = new HashMap<>();
//        response.put("leads", leads);
//        return ResponseEntity.ok(response);
//    }
    @GetMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Map<String, Page<LeadResponseDto>>> getLeadsBySearchAndPagination(
            @AuthenticationPrincipal EnsarUserDetails userDetails,
//            @RequestParam(defaultValue = "") String search,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        // Retrieve the current organization ID
        String orgId = userDetails.getOrganization().getId();

        // Fetch the leads with search and pagination
        Page<LeadResponseDto> leads = leadService.getLeadsByPagination(userDetails, page, size);

        // Prepare response
        Map<String, Page<LeadResponseDto>> response = new HashMap<>();
        response.put("leads", leads);

        return ResponseEntity.ok(response);
    }

    @GetMapping("/all")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Map<String, List<LeadResponseDto>>> getAllLeads(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        String organizationId = TenantContext.getCurrentTenant(); // Retrieve the current organization ID
        List<LeadResponseDto> leads = leadService.getAllLeads(orgId);
        Map<String, List<LeadResponseDto>> response = new HashMap<>();
        response.put("leads", leads);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/drafts")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Map<String, List<LeadResponseDto>>> getAllDraftLeads() {
        List<LeadResponseDto> leads = leadService.getAllDraftLeads();
        Map<String, List<LeadResponseDto>> response = new HashMap<>();
        response.put("leads", leads);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<LeadResponseDto> createLead(@Valid @RequestBody CreateUpdateLeadDto createUpdateLeadDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        LeadResponseDto leadDto = leadService.convertToDto(leadService.createOrUpdateLead(Optional.empty(), createUpdateLeadDto,organization));
        return ResponseEntity.ok(leadDto);
    }

    @PutMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<LeadResponseDto> updateLead(@PathVariable String id, @Valid @RequestBody CreateUpdateLeadDto createUpdateLeadDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        LeadResponseDto leadDto = leadService.convertToDto(leadService.createOrUpdateLead(Optional.of(id), createUpdateLeadDto,organization));
        return ResponseEntity.ok(leadDto);
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public ResponseEntity<Void> deleteLead(@PathVariable String id) {
        leadService.deleteLead(id);
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN', 'ROLE_USER')")
    public void exportLeads(HttpServletResponse response, @AuthenticationPrincipal EnsarUserDetails userDetails) {
        leadService.exportLeads(response,userDetails);
    }

//    @PostMapping("/import")
//    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
//    public ResponseEntity<String> importLeads(@RequestParam("file") MultipartFile file,@AuthenticationPrincipal EnsarUserDetails userDetails) {
//        Organization organization = userDetails.getOrganization();
//        leadService.importLeads(file,userDetails);
//        return ResponseEntity.ok("Sucessful");
//    }

    @PostMapping("/import")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Map<String, Object>> importLeads(
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal EnsarUserDetails userDetails) {

        Map<String, Object> result = leadService.importLeads(file, userDetails);
        return ResponseEntity.ok(result);
    }

    // Endpoint to delete a draft
    @DeleteMapping("/{id}/draft")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<String> deleteDraft(@PathVariable String id) {
        leadService.deleteDraftLead(id);
        return ResponseEntity.ok("Draft deleted successfully.");
    }
}

