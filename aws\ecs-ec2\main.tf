terraform {
  required_providers {
    aws = { source = "hashicorp/aws", version = "5.17.0" }
  }
}

provider "aws" {
  profile = "default"
  region  = "us-east-1"
}

data "aws_availability_zones" "available" {
  state = "available"
}

locals {
  azs_count = 2
  azs_names = data.aws_availability_zones.available.names
}

data "aws_vpc" "existing_vpc" {
  id = "vpc-04a2c587f616ccbec"
}

resource "aws_ecs_cluster" "main" {
  name = "pmoapi-api-ec2-cluster"
}

data "aws_iam_policy_document" "pmoapi_ecs_node_doc" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "pmoapi_ecs_node_role" {
  name_prefix        = "pmoapi-ecs-node-role"
  assume_role_policy = data.aws_iam_policy_document.pmoapi_ecs_node_doc.json
}

resource "aws_iam_role_policy_attachment" "pmoapi_ecs_node_role_policy" {
  role       = aws_iam_role.pmoapi_ecs_node_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonEC2ContainerServiceforEC2Role"
}

resource "aws_iam_instance_profile" "pmoapi_ecs_node" {
  name_prefix = "pmoapi-ecs-node-profile"
  path        = "/ecs/instance/"
  role        = aws_iam_role.pmoapi_ecs_node_role.name
}

resource "aws_security_group" "pmoapi_ecs_node_sg" {
  name_prefix = "pmoapi-ecs-node-sg-"
  vpc_id      = data.aws_vpc.existing_vpc.id

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

data "aws_ssm_parameter" "ecs_node_ami" {
  name = "/aws/service/ecs/optimized-ami/amazon-linux-2/recommended/image_id"
}

resource "aws_launch_template" "pmoapi_ecs_ec2" {
  name_prefix            = "pmoapi-ecs-ec2-"
  image_id               = data.aws_ssm_parameter.ecs_node_ami.value
  instance_type          = "t2.micro"
  vpc_security_group_ids = [aws_security_group.pmoapi_ecs_node_sg.id]

  iam_instance_profile { arn = aws_iam_instance_profile.pmoapi_ecs_node.arn }
  monitoring { enabled = true }

  user_data = base64encode(<<-EOF
      #!/bin/bash
      echo ECS_CLUSTER=${aws_ecs_cluster.main.name} >> /etc/ecs/ecs.config;
    EOF
  )
}

resource "aws_autoscaling_group" "ecs" {
  name_prefix               = "pmoapi-ecs-asg-"
  vpc_zone_identifier       =  ["subnet-0ae7d203da33826ed", "subnet-042ffd2f72856c77e"]
  min_size                  = 2
  max_size                  = 3
  health_check_grace_period = 0
  health_check_type         = "EC2"
  protect_from_scale_in     = false

  launch_template {
    id      = aws_launch_template.pmoapi_ecs_ec2.id
    version = "$Latest"
  }

  tag {
    key                 = "Name"
    value               = "pmoapi-ecs-cluster"
    propagate_at_launch = true
  }

  tag {
    key                 = "AmazonECSManaged"
    value               = ""
    propagate_at_launch = true
  }
}

resource "aws_ecs_capacity_provider" "main" {
  name = "pmoapi-ecs-ec2"

  auto_scaling_group_provider {
    auto_scaling_group_arn         = aws_autoscaling_group.ecs.arn
    managed_termination_protection = "DISABLED"

    managed_scaling {
      maximum_scaling_step_size = 2
      minimum_scaling_step_size = 1
      status                    = "ENABLED"
      target_capacity           = 100
    }
  }
}

resource "aws_ecs_cluster_capacity_providers" "main" {
  cluster_name       = aws_ecs_cluster.main.name
  capacity_providers = [aws_ecs_capacity_provider.main.name]

  default_capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.main.name
    base              = 1
    weight            = 100
  }
}

data "aws_iam_policy_document" "pmoapi_ecs_task_doc" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

resource "aws_iam_role" "pmoapi_ecs_task_role" {
  name_prefix        = "pmoapi-ecs-task-role"
  assume_role_policy = data.aws_iam_policy_document.pmoapi_ecs_task_doc.json
}

resource "aws_iam_role" "pmoapi_ecs_exec_role" {
  name_prefix        = "pmoapi-ecs-exec-role"
  assume_role_policy = data.aws_iam_policy_document.pmoapi_ecs_task_doc.json
}

resource "aws_iam_role_policy_attachment" "pmoapi_ecs_exec_role_policy" {
  role       = aws_iam_role.pmoapi_ecs_exec_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

resource "aws_cloudwatch_log_group" "ecs" {
  name              = "/ecs/pmoapi"
  retention_in_days = 14
}

resource "aws_ecs_task_definition" "pmoapi" {
  family             = "pmoapi-app"
  task_role_arn      = aws_iam_role.pmoapi_ecs_task_role.arn
  execution_role_arn = aws_iam_role.pmoapi_ecs_exec_role.arn
  network_mode       = "awsvpc"
  cpu                = 256
  memory             = 512

  container_definitions = jsonencode([
    {
      name        = "pmoapi-api",
      image       = "058264330686.dkr.ecr.us-east-1.amazonaws.com/pmo-api:latest",
      cpu         = 256,
      memory      = 512,
      essential   = true,
      portMappings = [
        {
          containerPort = 8080,
          hostPort      = 8080,
        },
      ],
      environment = [
       
        { name = "JWT_SECRET_KEY",                 value = "fWnJveXKFm0c/tll7gUOwXfXb4j4CbCvuYeYDEGO02M=" },
        { name = "JWT_TTL_MINS",                   value = "180" },
        { name = "DB_SERVER",                      value = "aurora-instance-1.c3muuqyyow9w.us-east-1.rds.amazonaws.com" },
        { name = "DB_PORT",                        value = "3306" },
        { name = "DB_SCHEMA",                      value = "pmo" },
        { name = "DB_USE_SSL",                     value = "true" },
        { name = "DB_REQUIRE_SSL",                 value = "true" },
        { name = "DB_USER",                        value = "admin" },
        { name = "DB_PASSWORD",                    value = "!Nellore123" },
        { name = "EMAIL_FROM",                     value = "<EMAIL>" },
        { name = "EMAIL_USER_NAME",                value = "your_email_username" },
        { name = "EMAIL_USER_PWD",                 value = "your_email_password" },
        { name = "EMAIL_HOST",                     value = "smtp.example.com" },
        { name = "EMAIL_PORT",                     value = "587" },
        { name = "AWS_ACCESS_KEY",                 value = "********************" },
        { name = "AWS_SECRET_KEY",                 value = "lwzRkH1mB027i1vJSe3YH9WF5YdwZNtvVHKiW86d" },
        { name = "AWS_ACCOUNT_ID",                 value = "************" },
        { name = "AWS_REGION",                     value = "us-east-1" },
        { name = "AWS_USER_ARN",                   value = "arn:aws:iam::************:user/java" },
        { name = "cloud.aws.credentials.accessKey", value = "********************" },
        { name = "cloud.aws.credentials.secretKey", value = "lwzRkH1mB027i1vJSe3YH9WF5YdwZNtvVHKiW86d" },
        { name = "cloud.aws.region.static",         value = "us-east-1" }
       
      ],
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-region"        = "us-east-1",
          "awslogs-group"         = aws_cloudwatch_log_group.ecs.name,
          "awslogs-stream-prefix" = "pmoapi-api"
        }
      },
    }
  ])
}

resource "aws_security_group" "pmoapi_ecs_task" {
  name_prefix = "pmoapi-ecs-task-sg-"
  description = "Allow all traffic within the VPC"
  vpc_id      = data.aws_vpc.existing_vpc.id

  ingress {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_lb" "app" {
  name               = "pmoapi-lb"
  internal           = false
  load_balancer_type = "application"
  security_groups    = [aws_security_group.pmoapi_ecs_task.id]
  subnets            = ["subnet-0ae7d203da33826ed", "subnet-042ffd2f72856c77e"]

  enable_deletion_protection = false
}

resource "aws_lb_target_group" "app" {
  name       = "pmoapi-tg"
  port       = 8080
  protocol   = "HTTP"
  vpc_id     = data.aws_vpc.existing_vpc.id
  target_type = "ip"

  health_check {
    healthy_threshold   = 2
    unhealthy_threshold = 2
    timeout             = 5
    interval            = 30
    path                = "/"
    matcher             = "200-399"
  }
}

resource "aws_lb_listener" "app" {
  load_balancer_arn = aws_lb.app.arn
  port              = "8080"
  protocol          = "HTTP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.app.arn
  }
}

resource "aws_ecs_service" "app" {
  name            = "app"
  cluster         = aws_ecs_cluster.main.id
  task_definition = aws_ecs_task_definition.pmoapi.arn
  desired_count   = 2

  network_configuration {
    security_groups = [aws_security_group.pmoapi_ecs_task.id]
    subnets         = ["subnet-0ae7d203da33826ed", "subnet-042ffd2f72856c77e"]
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.app.arn
    container_name   = "pmoapi-api"
    container_port   = 8080
  }

  capacity_provider_strategy {
    capacity_provider = aws_ecs_capacity_provider.main.name
    base              = 1
    weight            = 100
  }

  ordered_placement_strategy {
    type  = "spread"
    field = "attribute:ecs.availability-zone"
  }

  
}
