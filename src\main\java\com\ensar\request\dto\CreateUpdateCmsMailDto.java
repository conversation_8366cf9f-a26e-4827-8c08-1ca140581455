package com.ensar.request.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Schema(description = "Parameters required to create/update CMS Mail")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateCmsMailDto {

    @Schema(description = "Subject of the mail", required = true)
    @NotBlank(message = "Subject is required")
    @Size(max = 255)
    private String subject;

    @Schema(description = "Content of the mail", required = true)
    @NotBlank(message = "Content is required")
    private String content;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}
