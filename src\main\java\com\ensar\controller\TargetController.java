package com.ensar.controller;


import com.ensar.entity.Organization;
import com.ensar.entity.Target;
import com.ensar.request.dto.CreateUpdateTargetDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.TargetService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Tag(name = "Target Management")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/targets")
public class TargetController {

    private final TargetService targetService;

    @Autowired
    public TargetController(TargetService targetService) {
        this.targetService = targetService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<Target> getTargetById(@PathVariable String id) {
        Target target = targetService.getTargetById(id);
        return ResponseEntity.ok(target);
    }

    @GetMapping("/")
    public ResponseEntity<Map<String, Page<Target>>> getTargetsByPagination(@AuthenticationPrincipal EnsarUserDetails userDetails,
                                                                   @RequestParam(defaultValue = "0") int page,
                                                                   @RequestParam(defaultValue = "10") int size) {
        String orgId = userDetails.getOrganization().getId();
        Page<Target> targetList = targetService.getTargetsByPagination(orgId,page, size);
        Map<String, Page<Target>> response = new HashMap<>();
        response.put("targets", targetList);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/all")
    public ResponseEntity<Map<String, List<Target>>> getAllTargets(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<Target> targetList = targetService.getAllTargets(orgId);
        Map<String, List<Target>> response = new HashMap<>();
        response.put("targets", targetList);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    public ResponseEntity<Target> createTarget(@Valid @RequestBody CreateUpdateTargetDto createUpdateTargetDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Target target = targetService.createOrUpdateTarget(Optional.empty(), createUpdateTargetDto,organization);
        return ResponseEntity.ok(target);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Target> updateTarget(@PathVariable String id,
                                               @Valid @RequestBody CreateUpdateTargetDto createUpdateTargetDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Target target = targetService.createOrUpdateTarget(Optional.of(id), createUpdateTargetDto,organization);
        return ResponseEntity.ok(target);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTarget(@PathVariable String id) {
        targetService.deleteTarget(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/import")
    public ResponseEntity<Void> importTargets(@RequestParam("file") MultipartFile file) throws IOException {
        targetService.importTargets(file.getInputStream());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportTargets() throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        targetService.exportTargets(outputStream);
        byte[] bytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename("targets.csv")
                .build();
        headers.setContentDisposition(contentDisposition);

        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);
    }
}
