package com.ensar.service;

import com.ensar.entity.*;
import com.ensar.repository.LeadRepository;
import com.ensar.repository.LeadTaskRepository;
import com.ensar.repository.UserRepository;
import com.ensar.repository.UserTaskRepository;
import com.ensar.request.dto.CreateUpdateUserTaskDto;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;

import java.io.*;
import java.sql.Timestamp;
import java.util.*;

@Service
@Log4j2
@Transactional
public class UserTaskService {

    private final UserTaskRepository userTaskRepository;
    private final UserRepository userRepository;
    private final LeadRepository leadRepository;
    private final LeadTaskRepository leadTaskRepository;

    @Autowired
    public UserTaskService(UserTaskRepository userTaskRepository, UserRepository userRepository, LeadTaskRepository leadTaskRepository, LeadRepository leadRepository) {
        this.userTaskRepository = userTaskRepository;
        this.userRepository = userRepository;
        this.leadRepository = leadRepository;
        this.leadTaskRepository = leadTaskRepository;
    }

    public User getLoggedInUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        log.debug("Retrieving logged-in user");
        return userRepository.findByEmail(authentication.getName());
    }

    public UserTask getUserTaskById(String id) {
        log.debug("Fetching UserTask by ID: {}", id);
        return userTaskRepository.findById(id)
                .orElseThrow(() -> {
                    log.error("UserTask with ID {} not found", id);
                    return new RuntimeException("UserTask with ID " + id + " not found.");
                });
    }

    public Page<UserTask> getUserTasksByPagination(String orgId, int page, int size) {
        User loggedInUser = getLoggedInUser();
        Pageable pageable = PageRequest.of(page, size);

        if (loggedInUser.getRole().getRolePermission().equals(Role.RolePermission.ROLE_SUPER_ADMIN)) {
            return userTaskRepository.findByOrganizationId(orgId,pageable);
        }

        if (loggedInUser.getRole().getRolePermission().equals(Role.RolePermission.ROLE_ADMIN)) {
            String organizationId = loggedInUser.getOrganization().getId();
            return userTaskRepository.findByUser_OrganizationId(organizationId, pageable);
        }

        return userTaskRepository.findByUserId(loggedInUser.getId(), pageable);
    }

    public List<UserTask> getAllUserTasks(String orgId) {
        User loggedInUser = getLoggedInUser();

        if (loggedInUser.getRole().getRolePermission().equals(Role.RolePermission.ROLE_SUPER_ADMIN)) {
            return userTaskRepository.findByOrganizationId(orgId);
        }

        if (loggedInUser.getRole().getRolePermission().equals(Role.RolePermission.ROLE_ADMIN)) {
            String organizationId = loggedInUser.getOrganization().getId();
            return userTaskRepository.findByOrganizationId(organizationId);
        }

        return userTaskRepository.findByUserId(loggedInUser.getId());
    }

    public UserTask createOrUpdateUserTask(Optional<String> userTaskId,
                                           CreateUpdateUserTaskDto createUpdateUserTaskDto,
                                           Organization organization) {
        log.debug("Creating/Updating UserTask with data: {}", createUpdateUserTaskDto);
        UserTask userTask = userTaskId.map(id -> userTaskRepository.findById(id)
                        .orElseThrow(() -> {
                            log.error("UserTask with ID {} not found", id);
                            return new RuntimeException("UserTask with ID " + id + " not found.");
                        }))
                .orElseGet(UserTask::new);

        // Validate and set the User entity
        if (createUpdateUserTaskDto.getUserId() != null) {
            User user = userRepository.findById(createUpdateUserTaskDto.getUserId())
                    .orElseThrow(() -> {
                        log.error("User with ID {} not found", createUpdateUserTaskDto.getUserId());
                        return new RuntimeException("User with ID " + createUpdateUserTaskDto.getUserId() + " not found.");
                    });
            userTask.setUser(user);
        }

        // Validate and set the Lead entity
        Lead lead = leadRepository.findById(createUpdateUserTaskDto.getLeadId())
                .orElseThrow(() -> {
                    log.error("Lead with ID {} not found", createUpdateUserTaskDto.getLeadId());
                    return new RuntimeException("Lead with ID " + createUpdateUserTaskDto.getLeadId() + " not found.");
                });
        userTask.setLead(lead);

        // Convert Date to Timestamp directly
        Timestamp startTimestamp = createUpdateUserTaskDto.getStartDate() != null ? new Timestamp(createUpdateUserTaskDto.getStartDate().getTime()) : null;
        Timestamp endTimestamp = createUpdateUserTaskDto.getEndDate() != null ? new Timestamp(createUpdateUserTaskDto.getEndDate().getTime()) : null;

        // Set other fields
        userTask.setName(createUpdateUserTaskDto.getName());
        userTask.setDescription(createUpdateUserTaskDto.getDescription());
        userTask.setStartDate(startTimestamp);  // Set Timestamp
        userTask.setEndDate(endTimestamp);      // Set Timestamp
        userTask.setPriority(UserTask.Priority.valueOf(createUpdateUserTaskDto.getPriority()));
        userTask.setStatus(UserTask.Status.valueOf(createUpdateUserTaskDto.getStatus()));
        userTask.setOrganization(organization);

        log.debug("Saving UserTask: {}", userTask);
        return userTaskRepository.save(userTask);
    }

    public void deleteUserTask(String id) {
        log.debug("Deleting UserTask with ID: {}", id);
        userTaskRepository.deleteById(id);
    }

    public void importUserTasks(InputStream inputStream) throws IOException {
        log.debug("Importing UserTasks from input stream");
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        List<UserTask> userTasks = new ArrayList<>();
        Map<String, Integer> headerMap = new HashMap<>();

        if ((line = reader.readLine()) != null) {
            String[] headers = line.split(",");
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
        } else {
            throw new IOException("Empty CSV file");
        }

        while ((line = reader.readLine()) != null) {
            String[] fields = line.split(",");

            if (fields.length != headerMap.size()) {
                throw new IOException("Invalid CSV format. Each line must have the same number of fields as the header.");
            }

            UserTask userTask = new UserTask();
            try {
                String leadId = fields[headerMap.get("leadid")];
                Lead lead = leadRepository.findById(leadId)
                        .orElseThrow(() -> new RuntimeException("Lead with ID " + leadId + " not found."));
                userTask.setLead(lead);

                String userId = fields[headerMap.get("userid")];
                User user = userRepository.findById(userId)
                        .orElseThrow(() -> new RuntimeException("User with ID " + userId + " not found."));
                userTask.setUser(user);

                String name = fields[headerMap.get("name")];
                String description = fields[headerMap.get("description")];
                Timestamp startDate = Timestamp.valueOf(fields[headerMap.get("startdate")]);
                Timestamp endDate = Timestamp.valueOf(fields[headerMap.get("enddate")]);
                UserTask.Priority priority = UserTask.Priority.valueOf(fields[headerMap.get("priority")]);
                UserTask.Status stage = UserTask.Status.valueOf(fields[headerMap.get("stage")]);

                userTask.setName(name);
                userTask.setDescription(description);
                userTask.setStartDate(startDate);
                userTask.setEndDate(endDate);
                userTask.setPriority(priority);
                userTask.setStatus(stage);
            } catch (Exception e) {
                log.error("Error processing line: {}", line, e);
                throw new IOException("Error processing line: " + line, e);
            }

            userTasks.add(userTask);
        }
        userTaskRepository.saveAll(userTasks);
        log.debug("UserTasks imported successfully");
    }

    public void exportUserTasks(OutputStream outputStream) throws IOException {
        log.debug("Exporting UserTasks to output stream");
        List<UserTask> userTasks = userTaskRepository.findAll();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("Name,Description,StartDate,EndDate,Priority,Stage,LeadId,UserId\n");
        for (UserTask userTask : userTasks) {
            writer.write(String.format("%s,%s,%s,%s,%s,%s,%s,%s\n",
                    userTask.getName(),
                    userTask.getDescription(),
                    userTask.getStartDate(),
                    userTask.getEndDate(),
                    userTask.getPriority(),
                    userTask.getStatus(),
                    userTask.getLead() != null ? userTask.getLead().getId() : "N/A",
                    userTask.getUser() != null ? userTask.getUser().getId() : "N/A"));
        }
        writer.flush();
        log.debug("UserTasks exported successfully");
    }
}
