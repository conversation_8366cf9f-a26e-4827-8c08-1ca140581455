package com.ensar.request.dto;

import com.ensar.util.Constants;


import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;



@Schema(description = "Parameters required to create/update ForecastDashBoard")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateForecastDashBoardDto {

    @Schema(description = "ForecastDashBoard Name", required = true)
    @NotBlank(message = "ForecastDashBoard Name is required")
    @Size(max = 50)
    private String name;

    @Schema(description = "ForecastDashBoard Description", required = false)
    @Size(max = 500)
    private String description;

    @Schema(description = "Forecast dashboard ID")
    @NotBlank(message = "ForecastDashBoard ID is required")
    @Pattern(regexp = Constants.UUID_PATTERN, message = "Invalid Forecast dashboard ID")
    private String dashBoardId;

    @Schema(description = "Organization")
    @NotBlank(message = "ForecastDashBoard Organization is required")
    private String organizationId;

}
