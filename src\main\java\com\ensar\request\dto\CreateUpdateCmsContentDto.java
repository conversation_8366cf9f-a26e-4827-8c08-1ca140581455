package com.ensar.request.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Schema(description = "Parameters required to create/update CMS Content")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateCmsContentDto {

    @Schema(description = "Title of the CMS Content", required = true)
    @NotBlank(message = "Title is required")
    @Size(max = 255)
    private String title;

    @Schema(description = "Description of the CMS Content", required = false)
    @Size(max = 500)
    private String description;

    @Schema(description = "Content text of the CMS", required = true)
    @NotBlank(message = "Content is required")
    private String content;

    @Schema(description = "Meta title for SEO", required = true)
    @NotBlank(message = "Meta title is required")
    @Size(max = 25)
    private String metaTitle;

    @Schema(description = "Meta tags for SEO", required = true)
    @NotBlank(message = "Meta tags are required")
    @Size(max = 25)
    private String metaTags;

    @Schema(description = "Meta description for SEO", required = false)
    @Size(max = 500)
    private String metaDescription;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;

    @Schema(description = "Meta keywords for SEO", required = true)
    @NotBlank(message = "Meta keywords are required")
    @Size(max = 25)
    private String metaKeywords;

    // New field for storing the URL of the cover image
    @Schema(description = "URL of the cover image", required = false)
    private String coverUrl;

    // New field for storing the binary data of the cover image
    @Schema(description = "Binary data of the cover image", required = false)
    private byte[] coverUrlData;
}
