provider "aws" {
  region = "us-east-1"
}

data "aws_s3_bucket" "existing_bucket" {
  bucket = "ensarcodebuild"
}

resource "aws_iam_role" "crmapi_codebuild_role" {
  name = "crmapi_codebuild-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Principal = {
          Service = "codebuild.amazonaws.com"
        },
        Effect = "Allow",
        Sid    = ""
      }
    ]
  })
}

resource "aws_iam_role_policy" "crmapi_codebuild_policy" {
  name   = "crm_codebuild-policy"
  role   = aws_iam_role.crmapi_codebuild_role.id
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = [
          "s3:GetObject",
          "s3:GetObjectVersion",
          "s3:PutObject",
          "secretsmanager:GetSecretValue",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetAuthorizationToken",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload",
          "ecr:PutImage"
        ],
        Resource = "*"
      },
      {
        Effect   = "Allow",
        Action   = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_codebuild_project" "crmapi_codebuild_project" {
  name          = "crmapi-api-codebuild-project"
  description   = "CodeBuild project for Java application using Gradle"
  build_timeout = 20

  source {
    type            = "GITHUB"
    location        = "https://github.com/nproducts2/crm-api"
    git_clone_depth = 1
    buildspec       = "buildspec.yml"
  }

  artifacts {
    type     = "S3"
    location = data.aws_s3_bucket.existing_bucket.bucket
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/standard:6.0"  # Update if needed
    type                        = "LINUX_CONTAINER"
    privileged_mode             = false
    image_pull_credentials_type = "CODEBUILD"
    environment_variable {
      name  = "GRADLE_OPTS"
      value = "-Dorg.gradle.daemon=false -Dorg.gradle.parallel=false -Dorg.gradle.configureondemand=false"
    }
    environment_variable {
      name  = "JAVA_HOME"
      value = "/usr/lib/jvm/java-17-amazon-corretto"  # Update this with the correct path if needed
    }
  }

  service_role = aws_iam_role.crmapi_codebuild_role.arn

  logs_config {
    cloudwatch_logs {
      group_name  = "contract-log-group"
      stream_name = "contract-log-stream"
    }
  }
}

resource "aws_codebuild_source_credential" "github_cred" {
  auth_type   = "PERSONAL_ACCESS_TOKEN"
  server_type = "GITHUB"
  token       = "****************************************"
}