package com.ensar.controller;

import com.ensar.entity.Company;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateCompanyDto;
import com.ensar.response.dto.CompanyResponseDto;
import com.ensar.response.dto.LeadResponseDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.CompanyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/companies")
@CrossOrigin(origins = "*", maxAge = 3600)
public class CompanyController {

    @Autowired
    private CompanyService companyService;

    @GetMapping
    public ResponseEntity<Map<String,List<CompanyResponseDto>>> getAllCompanies(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<CompanyResponseDto> companies = companyService.getAllCompanies(orgId);
        Map<String, List<CompanyResponseDto>> response = new HashMap<>();
        response.put("companies", companies);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public CompanyResponseDto getCompanyById(@PathVariable String id) {
        return companyService.getCompanyById(id);
    }

    @PostMapping
    public CompanyResponseDto createCompany(@RequestBody CreateUpdateCompanyDto company,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        return companyService.saveCompany(company,organization);
    }

    @PutMapping("/{id}")
    public CompanyResponseDto updateCompany(@PathVariable String id, @RequestBody CreateUpdateCompanyDto company,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        return companyService.saveCompany(company,organization);
    }

    @DeleteMapping("/{id}")
    public void deleteCompany(@PathVariable String id) {
        companyService.deleteCompany(id);
    }
}
