package com.ensar.mapper;

import com.ensar.entity.Industry;
import com.ensar.request.dto.CreateUpdateIndustryDto;
import com.ensar.response.dto.IndustryResponseDto;

public class IndustryMapper {

    public static Industry toEntity(CreateUpdateIndustryDto dto) {
        Industry industry = new Industry();
        industry.setId(dto.getId());
        industry.setName(dto.getName());
        return industry;
    }

    public static IndustryResponseDto toDto(Industry industry) {
        IndustryResponseDto dto = new IndustryResponseDto();
        dto.setId(industry.getId());
        dto.setName(industry.getName());
        
        return dto;
    }
}
