DROP TABLE IF EXISTS `companies`;

CREATE TABLE companies
(
    id                    CHAR(36)    NOT NULL,
    name                  <PERSON><PERSON><PERSON><PERSON>(100) NOT NULL,
    industry_id           CHAR(36)     NULL,
    website               VARCHAR(255) NULL,
    region                VARCHAR(50) NULL,
    emp_count              INT NULL,
    organization_id CHAR(36) NOT NULL,
    `created_date_time`      timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp NULL     DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_companies_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_companies_industry FOREIGN KEY (industry_id) REFERENCES industries(id) ON DELETE CASCADE

) ENGINE = InnoDB
DEFAULT CHARSET = utf8;