set @ORG_ID = uuid();
INSERT INTO organization
(id, name, description, domain)
VALUES(@ORG_ID, 'Demo Org', 'Demo desc', 'demo-domain');

SET @ORG_ID = (SELECT id FROM organization LIMIT 1);

-- Insert roles into the role table
SET @ROLE_SUPER_ADMIN_ID = uuid();
SET @ROLE_ADMIN_ID = uuid();
SET @ROLE_USER_ID = uuid();

INSERT INTO `role`
(id, role_name, role_description, role_permission)
VALUES
(@ROLE_SUPER_ADMIN_ID, 'ROLE_SUPER_ADMIN', 'Super Administrator', 'ALL_PERMISSIONS'),
(@ROLE_ADMIN_ID, 'ROLE_ADMIN', 'Administrator', 'ADMIN_PERMISSIONS'),
(@ROLE_USER_ID, 'ROLE_USER', 'Regular User', 'USER_PERMISSIONS');

-- Generate user IDs
SET @USER_ID_KALYAN = uuid();
SET @USER_ID_SRIDHAR = uuid();

-- Insert users with appropriate role_id
INSERT INTO `user`
(id, first_name, last_name, email, role_id, organization_id)
VALUES
(@USER_ID_KALYAN, 'Kalyan', 'K', '<EMAIL>', @ROLE_SUPER_ADMIN_ID, @ORG_ID),
(@USER_ID_SRIDHAR, 'Sridhar', 'P', '<EMAIL>', @ROLE_SUPER_ADMIN_ID, @ORG_ID);
