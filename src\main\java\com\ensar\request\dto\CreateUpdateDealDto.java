package com.ensar.request.dto;


import com.ensar.entity.Deal;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.sql.Date;

@Schema( description = "Parameters required to create/update a deal")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateDealDto {

    @Schema(description = "Deal Name", required = true)
    @NotBlank(message = "Deal name is required")
    private String name;

    @Schema(description = "Lead ID", required = true)

    private String leadId;

    @Schema(description = "Contact Email", required = true)
    @NotBlank(message = "Email is required")
    @Email(message = "Email should be valid")
    private String email;

    @Schema(description = "Deal Stage", required = true)
    @NotNull(message = "Deal stage is required")
    private Deal.Stage stage;

    @Schema(description = "Deal Value", required = true)
    @NotNull(message = "Deal value is required")
    private BigDecimal value;

    @Schema(description = "Expected Close Date", required = true)
    @NotNull(message = "Expected close date is required")
    private Date expectedCloseDate;

    @Schema(description = "Actual Close Date")
    private Date actualCloseDate;

    @Schema(description = " Status", required = true)
    @NotNull(message = "Customer status is required")
    private Deal.Status status;

    @Schema(description = "Priority", required = true)
    @NotNull(message = "Priority is required")
    private Deal.Priority priority;

    @Schema(description = "Source of Deal")
    private Deal.Source source;

    @Schema(description = "Next Step")
    private String nextStep;

    @Schema(description = "Additional Notes")
    private String notes;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}



