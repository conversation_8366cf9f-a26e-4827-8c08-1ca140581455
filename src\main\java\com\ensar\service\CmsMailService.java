package com.ensar.service;

import com.ensar.entity.CmsMail;
import com.ensar.entity.Organization;
import com.ensar.repository.CmsMailRepository;
import com.ensar.request.dto.CreateUpdateCmsMailDto;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.*;
import java.util.*;

@Service
@Log4j2
@Transactional
public class CmsMailService {

    private final CmsMailRepository cmsMailRepository;

    @Autowired
    public CmsMailService(CmsMailRepository cmsMailRepository) {
        this.cmsMailRepository = cmsMailRepository;
    }

    // Get a single CmsMail by ID
    public CmsMail getCmsMailById(String id) {
        Optional<CmsMail> cmsMailOptional = cmsMailRepository.findById(id);
        if (!cmsMailOptional.isPresent())
            throw new RuntimeException("CmsMail with ID " + id + " not found.");
        return cmsMailOptional.get();
    }

    // Get all CmsMail records
    public List<CmsMail> getAllCmsMail(String orgId) {
        return cmsMailRepository.findByOrganizationId(orgId);
    }

    // Create or update a CmsMail
    public CmsMail createOrUpdateCmsMail(Optional<String> cmsMailId,
                                         CreateUpdateCmsMailDto createUpdateCmsMailDto,
                                         Organization organization) {
        CmsMail cmsMail;
        if (cmsMailId.isPresent()) {
            cmsMail = cmsMailRepository.findById(cmsMailId.get())
                    .orElseThrow(() -> new RuntimeException("CmsMail with ID " + cmsMailId.get() + " not found"));
        } else {
            cmsMail = new CmsMail();
        }

        // Setting properties from DTO
        cmsMail.setSubject(createUpdateCmsMailDto.getSubject());
        cmsMail.setContent(createUpdateCmsMailDto.getContent());
        cmsMail.setOrganization(organization);

        return cmsMailRepository.save(cmsMail);
    }

    // Delete a CmsMail by ID
    public void deleteCmsMail(String id) {
        cmsMailRepository.deleteById(id);
    }

    // Import CmsMail data from CSV file
    public void importCmsMail(InputStream inputStream) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream));
        String line;
        List<CmsMail> cmsMails = new ArrayList<>();
        Map<String, Integer> headerMap = new HashMap<>();

        // Read the CSV headers
        if ((line = reader.readLine()) != null) {
            String[] headers = line.split(",");
            for (int i = 0; i < headers.length; i++) {
                headerMap.put(headers[i].trim().toLowerCase(), i);
            }
        } else {
            throw new IOException("Empty CSV file");
        }

        // Read each line in the CSV and map values to CmsMail object
        while ((line = reader.readLine()) != null) {
            String[] fields = line.split(",");

            if (fields.length != headerMap.size()) {
                throw new IOException("Invalid CSV format. Each line must have the same number of fields as the header.");
            }

            CmsMail cmsMail = new CmsMail();
            try {
                // Extract the data from CSV using the header map
                String subject = fields[headerMap.get("subject")];
                String content = fields[headerMap.get("content")];

                // Set values in CmsMail entity
                cmsMail.setSubject(subject);
                cmsMail.setContent(content);
            } catch (Exception e) {
                throw new IOException("Error processing line: " + line, e);
            }

            cmsMails.add(cmsMail);
        }
        // Save all CmsMails from CSV into database
        cmsMailRepository.saveAll(cmsMails);
    }

    // Export CmsMail data to CSV file
    public void exportCmsMail(OutputStream outputStream) throws IOException {
        List<CmsMail> cmsMails = cmsMailRepository.findAll();
        BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(outputStream));
        writer.write("Subject,Content\n");
        for (CmsMail cmsMail : cmsMails) {
            writer.write(String.format("%s,%s\n",
                    cmsMail.getSubject(),
                    cmsMail.getContent()));
        }
        writer.flush();
    }
}
