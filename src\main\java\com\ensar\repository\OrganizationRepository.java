package com.ensar.repository;

import com.ensar.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.ensar.entity.Organization;

@Repository
public interface OrganizationRepository extends JpaRepository<Organization, String> {
    Organization findByName(String name);

    boolean existsByName(String name);

    Organization findByNameIgnoreCase(String name);
}
