package com.ensar.repository;



import com.ensar.entity.Organization;
import com.ensar.entity.Project;
import com.ensar.entity.Target;
import com.ensar.entity.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import org.springframework.data.domain.Page;

import org.springframework.data.domain.Pageable;
import java.util.List;

@Repository
public interface TargetRepository extends JpaRepository<Target, String> {

    List<Target> findByOrganizationId(String organizationId);
    Page<Target> findByOrganizationId(String orgId, Pageable pageable);

    List<Target> findByHandledBy(User user);
    Page<Target> findByHandledBy(User user, Pageable pageable);
}
