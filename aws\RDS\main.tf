provider "aws" {
  region = "us-east-1"  
}

data "aws_vpc" "selected" {
  id = "vpc-04a2c587f616ccbec" 
}

data "aws_subnets" "selected" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.selected.id]
  }
}

resource "aws_security_group" "rds_sg" {
  name        = "ensarproducts-rds-sg"
  description = "Security group for ensarproducts MySQL RDS instance"
  vpc_id      = data.aws_vpc.selected.id

  ingress {
    description = "MySQL access"
    from_port   = 3306
    to_port     = 3306
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]  
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name = "ensarproducts-rds-security-group"
  }
}

resource "aws_db_instance" "mysql_db" {
  identifier              = "ensarproducts-mysql-db"
  allocated_storage       = 20                
  engine                  = "mysql"
  engine_version          = "8.0"
  instance_class          = "db.t3.micro"        
  username                = "admin"              
  password                = "!Ensar123"          
  db_name                 = "ensarproductsdb"
  vpc_security_group_ids  = [aws_security_group.rds_sg.id]
  db_subnet_group_name    = aws_db_subnet_group.rds_subnet_group.name
  skip_final_snapshot     = true                 
  publicly_accessible     = true

  backup_retention_period = 7
  backup_window           = "07:00-09:00"
  
  maintenance_window      = "Mon:03:00-Mon:04:00"

  tags = {
    Name = "ensarproducts-mysql-rds"
  }
}

resource "aws_db_subnet_group" "rds_subnet_group" {
  name       = "ensarproducts-rds-subnet-group"
  subnet_ids = data.aws_subnets.selected.ids

  tags = {
    Name = "ensarproducts-rds-subnet-group"
  }
}
