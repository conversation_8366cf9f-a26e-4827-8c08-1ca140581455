package com.ensar.service;

import com.ensar.entity.Industry;
import com.ensar.entity.Organization;
import com.ensar.entity.Project;
import com.ensar.mapper.IndustryMapper;
import com.ensar.repository.IndustryRepository;
import com.ensar.request.dto.CreateUpdateIndustryDto;
import com.ensar.response.dto.IndustryResponseDto;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.http.HttpStatus;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class IndustryService {

    @Autowired
    private IndustryRepository industryRepository;

//    public List<IndustryResponseDto> getAllIndustries() {
//        return industryRepository.findAll().stream()
//            .map(IndustryMapper::toDto)
//            .collect(Collectors.toList());
//    }

    public List<Industry> getAllIndustries(String orgId) {
        return industryRepository.findByOrganizationId(orgId);
    }

    public IndustryResponseDto getIndustryById(String id) {
        Industry industry = industryRepository.findById(id)
            .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Industry not found"));
        return IndustryMapper.toDto(industry);
    }

    public IndustryResponseDto saveIndustry(CreateUpdateIndustryDto dto, Organization organization) {
        Industry industry = IndustryMapper.toEntity(dto);
        industry.setOrganization(organization);
        industry = industryRepository.save(industry);
        return IndustryMapper.toDto(industry);
    }

    public void deleteIndustry(String id) {
        if (!industryRepository.existsById(id)) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, "Industry not found");
        }
        industryRepository.deleteById(id);
    }
}
