package com.ensar.repository;

import com.ensar.entity.Project;
import com.ensar.entity.User;
import com.ensar.entity.UserTask;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.sql.Date;
import java.util.List;

@Repository
public interface UserTaskRepository extends JpaRepository<UserTask, String> {

    UserTask findByName(String name);

    boolean existsByName(String name);



    List<UserTask> findByPriorityAndStatus(String priority, String status);

    Page<UserTask> findByUserId(String userId, Pageable pageable);

    Page<UserTask> findByUser_OrganizationId(String organizationId, Pageable pageable);

    Page<UserTask> findByOrganizationId(String organizationId, Pageable pageable);

    List<UserTask> findByUserId(String userId);

    List<UserTask> findByOrganizationId(String organizationId);
}
