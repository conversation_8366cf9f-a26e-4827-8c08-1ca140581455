package com.ensar.service;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import jakarta.annotation.PostConstruct;
import jakarta.mail.MessagingException;
import jakarta.mail.internet.MimeMessage;
import jakarta.transaction.Transactional;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.stereotype.Service;

import com.ensar.entity.Organization;
import com.ensar.entity.Role;
import com.ensar.entity.User;
import com.ensar.entity.UserLoginTracker;
import com.ensar.entity.UserPasswordResetRequest;
import com.ensar.repository.OrganizationRepository;
import com.ensar.repository.RoleRepository;
import com.ensar.repository.UserLoginTrackerRepository;
import com.ensar.repository.UserPasswordResetRequestRepository;
import com.ensar.repository.UserRepository;
import com.ensar.request.dto.CreateUpdateUserDto;
import com.ensar.security.EnsarUserDetails;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Log4j2
@Transactional
public class UserService implements UserDetailsService {

    private UserRepository userRepository;
    private OrganizationRepository organizationRepository;
    private RoleRepository roleRepository;
    private final UserPasswordResetRequestRepository userPasswordResetRequestRepository;
    private UserLoginTrackerRepository userLoginTrackerRepository;
    private BCryptPasswordEncoder bCryptEncoder;
    private EmailSender emailSender;
    private final JavaMailSender mailSender;
    private final TotpService totpService;

    @Value("${app.url.prefix:}")
    private String serverUrlPrefix;

    @Value("${app.allowed.email.domains:ensarsolutions.com,vizenanalytics.com}")
    private String alwaysAllowedEmailDomainsSettingStr;

    private List<String> alwaysAllowedEmailDomains;

    @Autowired
    public UserService(UserRepository userRepository,
                       OrganizationRepository organizationRepository,
                       UserPasswordResetRequestRepository userPasswordResetRequestRepository,
                       RoleRepository roleRepository,
                       BCryptPasswordEncoder bCryptEncoder,
                       UserLoginTrackerRepository userLoginTrackerRepository,
                       EmailSender emailSender, JavaMailSender mailSender, TotpService totpService) {
        this.userRepository = userRepository;
        this.organizationRepository = organizationRepository;
        this.roleRepository = roleRepository;
        this.userPasswordResetRequestRepository = userPasswordResetRequestRepository;
        this.userLoginTrackerRepository = userLoginTrackerRepository;
        this.bCryptEncoder = bCryptEncoder;
        this.emailSender = emailSender;
        this.mailSender = mailSender;
        this.totpService = totpService;
    }

    @PostConstruct
    public void init() {
        alwaysAllowedEmailDomains = Arrays.stream(alwaysAllowedEmailDomainsSettingStr.split(","))
                .map(a -> a.toLowerCase().trim()).collect(Collectors.toList());
        log.info("### Always allowed email domains : " + alwaysAllowedEmailDomains);
    }

    public User getUserById(String id) {
        Optional<User> userOptional = userRepository.findById(id);
        if (!userOptional.isPresent())
            throw new RuntimeException("User with " + id + " not found.");

        return userOptional.get();
    }

    public Page<User> getAllAccessibleUsersByPaganization(String orgId,int page, int size) {
        User user = getLoggedInUser();
        Pageable pageable = PageRequest.of(page, size);
        // Validate if the user has a role
        if (user.getRole() == null) {
            throw new RuntimeException("User role is not assigned.");
        }

        // If the user is a SUPER_ADMIN, return all users or users by organization
        if (Role.RolePermission.ROLE_SUPER_ADMIN.equals(user.getRole().getRolePermission())) {
            if (orgId != null) {
                return userRepository.findByOrganizationId(orgId, pageable);
            }
            return userRepository.findAll(pageable);
        }

        // If the user is an ADMIN, return users of the same organization
        if (Role.RolePermission.ROLE_ADMIN.equals(user.getRole().getRolePermission())) {
            return userRepository.findByOrganization(user.getOrganization(), pageable);
        }

//        if (user.getRole().getRolePermission().equals(Role.RolePermission.ROLE_USER)) { // Regular users can only access their own user details
//            return List.of(userRepository.findById(user.getId())
//                    .orElseThrow(() -> new RuntimeException("User not found")));
//        }

        if (user.getRole().getRolePermission().equals(Role.RolePermission.ROLE_USER)) {
            return new PageImpl<>(List.of(user), pageable, 1);
        }

        // For any other role (default case), return only the logged-in user's details
        return Page.empty();
    }

    public List<User> getAllAccessibleUsers(String orgId) {
        User user = getLoggedInUser();
        // Validate if the user has a role
        if (user.getRole() == null) {
            throw new RuntimeException("User role is not assigned.");
        }

        // If the user is a SUPER_ADMIN, return all users or users by organization
        if (Role.RolePermission.ROLE_SUPER_ADMIN.equals(user.getRole().getRolePermission())) {
            if (orgId != null) {
                return userRepository.findByOrganizationId(orgId);
            }
            return userRepository.findAll();
        }

        // If the user is an ADMIN, return users of the same organization
        if (Role.RolePermission.ROLE_ADMIN.equals(user.getRole().getRolePermission())) {
            return userRepository.findByOrganization(user.getOrganization());
        }

        if (user.getRole().getRolePermission().equals(Role.RolePermission.ROLE_USER)) { // Regular users can only access their own user details
            return List.of(userRepository.findById(user.getId())
                    .orElseThrow(() -> new RuntimeException("User not found")));
        }



        // For any other role (default case), return only the logged-in user's details
        return List.of();
    }

    public User getUserByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    public User findByUsername(String username) {
        Optional<User> user = userRepository.findByEmailIgnoreCase(username);
        return user.orElseThrow(() -> new RuntimeException("User not found with username: " + username));
    }

    public User updatePassword(String token, String newPassword) {
        UserPasswordResetRequest userPasswordResetRequest = userPasswordResetRequestRepository.getById(token);
        User user = userRepository.getById(userPasswordResetRequest.getUserId());
        user.setPassword(bCryptEncoder.encode(newPassword));
        userRepository.save(user);
        String subject = "Ensar Account password successfully changed.";
        String message = "You've successfully updated your password for your Ensar Solutions Account. Please login to your account" +
                " using link : " + serverUrlPrefix;
        emailSender.sendSimpleMessage(user.getEmail(), subject, null, null); // No password needed here

        return user;
    }

    public UserPasswordResetRequest createPasswordResetRequest(String email, boolean newUser) {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            return null;
        }

        UserPasswordResetRequest userPasswordResetRequest = new UserPasswordResetRequest();
        userPasswordResetRequest.setUserId(user.getId());
        userPasswordResetRequest.setExpireDateTime(Timestamp.valueOf(LocalDateTime.now().plusHours(2)));
        userPasswordResetRequestRepository.save(userPasswordResetRequest);

        String resetLink = generateResetPasswordUrl(userPasswordResetRequest.getId());

//        if (newUser) {
//            // Send welcome email with credentials
//            sendUserCredentialsEmail(user.getEmail(), user.getPassword(), null);
//        } else {
//            // Send password reset email
//            sendUserCredentialsEmail(user.getEmail(), null, resetLink);
//        }

        return userPasswordResetRequest;
    }


    public UserPasswordResetRequest getPasswordResetRequest(String id) {
        return userPasswordResetRequestRepository.getById(id);
    }

    @Override
    public EnsarUserDetails loadUserByUsername(String email) throws UsernameNotFoundException {
        User user = userRepository.findByEmail(email);
        if (user == null) {
            throw new UsernameNotFoundException("Invalid username.");
        }
        return new EnsarUserDetails(user);
    }

    public User createOrUpdateUser(Optional<String> userId,
                                   CreateUpdateUserDto createUpdateUserDto,
                                   Organization org) {
        User user;
        User currentUser = getLoggedInUser();
        if (userId.isPresent()) {
            user = userRepository.findById(userId.get())
                    .orElseThrow(() -> new RuntimeException("User with id " + userId.get() + " not found"));
        } else {
            if (userRepository.existsByEmail(createUpdateUserDto.getEmail())) {
                throw new RuntimeException("User with email " + createUpdateUserDto.getEmail() + " already exists.");
            }
            user = new User();
        }

        String organizationId = org.getId();
        if (organizationId == null || organizationId.trim().isEmpty()) {
            throw new RuntimeException("Organization ID must not be null or empty.");
        }

//        if (user.getOrganization() == null || !organizationId.equals(user.getOrganization().getId())) {
//            Organization organization = organizationRepository.findById(organizationId)
//                    .orElseThrow(() -> new RuntimeException("Organization with id " + organizationId + " not found."));
//            user.setOrganization(organization);
//        }

        if(currentUser.getEmail().trim().toLowerCase().equals("<EMAIL>")){
            System.out.println(createUpdateUserDto.getOrganizationId());
            if(!createUpdateUserDto.getOrganizationId().trim().isEmpty()){
                Organization organization = organizationRepository.findById(createUpdateUserDto.getOrganizationId())
                        .orElseThrow(() -> new RuntimeException("Organization with id " + createUpdateUserDto.getOrganizationId() + " not found"));
                user.setOrganization(organization);
            }else {
                user.setOrganization(org);
            }
        } else {
            user.setOrganization(org);
        }
        if (!userId.isPresent()) {
            String userEmailDomain = createUpdateUserDto.getEmail().substring(createUpdateUserDto.getEmail().indexOf("@") + 1).toLowerCase().trim();
            if (!alwaysAllowedEmailDomains.contains(userEmailDomain)
                    && !user.getOrganization().getDomain().trim().equalsIgnoreCase(userEmailDomain)) {
                throw new RuntimeException("Invalid Email domain, must be " + user.getOrganization().getDomain());
            }

            if (Role.RolePermission.ROLE_ADMIN.equals(currentUser.getRole().getRolePermission())
                    && !currentUser.getOrganization().getId().equals(organizationId)) {
                throw new RuntimeException("Invalid organization for the current admin user.");
            }
            user.setPassword(bCryptEncoder.encode(createUpdateUserDto.getPassword()));
        }

        String roleId = createUpdateUserDto.getRoleId();
        if (roleId == null || roleId.trim().isEmpty()) {
            throw new RuntimeException("Role ID must not be null or empty.");
        }
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new RuntimeException("Role with id " + roleId + " not found."));
        user.setRole(role);

        user.setEmail(createUpdateUserDto.getEmail());
        user.setFirstName(createUpdateUserDto.getFirstName());
        user.setLastName(createUpdateUserDto.getLastName());
        user.setSignUpMethod("email");
        user.setCity(createUpdateUserDto.getCity());
        user.setCountry(createUpdateUserDto.getCountry());
        user.setState(createUpdateUserDto.getState());
        user.setStatus(createUpdateUserDto.getStatus());
        user.setDisabled(createUpdateUserDto.isDisabled());
        user.setAddress(createUpdateUserDto.getAddress());
        user.setZipCode(createUpdateUserDto.getZipCode());
        user.setPhoneNumber(createUpdateUserDto.getPhoneNumber());
        user.setCompany(createUpdateUserDto.getCompany());
        user.setVerified(createUpdateUserDto.isVerified());
        user.setEmailVerified(createUpdateUserDto.isEmailVerified());
//        user.setOrganization(org);

        user = userRepository.save(user);

        // Send welcome email with credentials only for new users
//        if (!userId.isPresent()) {
//            sendUserCredentialsEmail(user.getEmail(), createUpdateUserDto.getPassword(), null);
//
//        }

        return user;
    }

//    public void sendUserCredentialsEmail(String email, String password, String resetLink) {
//        String subject;
//        String messageBody;
//
//        if (password != null) {
//            // Account creation (welcome email) content
//            subject = "Welcome to Ensar Solutions";
//            messageBody = String.format("<html><body><h3>Dear User,</h3><p>Your account has been created successfully.</p>"
//                    + "<p><b>Email:</b> %s<br><b>Password:</b> %s</p>"
//                    + "<p>Please login using the credentials above.</p>"
//                    + "<p>Best Regards,<br>Ensar Team</p></body></html>", email, password);
//        } else if (resetLink != null) {
//            // Password reset email content
//            subject = "Reset your Ensar Solutions password";
//            messageBody = String.format("<html><body><h3>Dear User,</h3>"
//                    + "<p>To reset your Ensar Solutions account password, please use the following link:</p>"
//                    + "<a href='%s'>Reset Password</a>"
//                    + "<p>Thank you!</p>"
//                    + "<p>Best Regards,<br>Ensar Team</p></body></html>", resetLink);
//        } else {
//            log.error("Insufficient information to send email");
//            return;
//        }
//
//        emailSender.sendSimpleMessage(email, subject, messageBody, null);
//    }




//
//    public void sendSimpleMessage(String email, String subject, String message) {
//        emailSender.sendSimpleMessage(email, subject, message, null);
//    }


    public boolean deleteUser(String id) {
        if (userRepository.existsById(id)) {
            userRepository.deleteById(id);
            return true;
        } else {
            return false;
        }
    }

    public User getLoggedInUser() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        sendLoginNotification(authentication.getName());
        return getUserByEmail(authentication.getName());
    }

    public void enableOrDisableUsers(List<String> userIdList, final boolean disabled) {
        userIdList.forEach(id -> {
            userRepository.getById(id).setDisabled(disabled);
        });
    }

    private String generateResetPasswordUrl(String token) {
        return serverUrlPrefix + "/reset-password?token=" + token;
    }

    public String createLoginTracker(String email, String userIp) {
        UserLoginTracker userLoginTracker = new UserLoginTracker();
        userLoginTracker.setUserIp(userIp);
        userLoginTracker.setUserEmail(email);
        userLoginTrackerRepository.save(userLoginTracker);
        return userLoginTracker.getId();
    }

    public void markLoginSuccess(String id) {
        UserLoginTracker userLoginTracker = userLoginTrackerRepository.getById(id);
        userLoginTracker.setSucceeded(true);
        userLoginTrackerRepository.save(userLoginTracker);
        User user = userRepository.findByEmail(userLoginTracker.getUserEmail());
        user.setLastLoginDateTime(Timestamp.valueOf(LocalDateTime.now()));
        userRepository.save(user);
    }

    public void sendLoginNotification(String email) {
        User user = userRepository.findByEmail(email);
        String subject = "Login Alert";
        String body = "Dear " + user.getFirstName() + ",\n\n"
                + "You have successfully logged into your account.\n\n"
                + "If this was not you, please reset your password immediately.\n\n"
                + "Regards,\nYour Ensar CRM Team";

        sendEmail(user.getEmail(), subject, body);
    }

    private void sendEmail(String to, String subject, String text) {
        try {
            MimeMessage message = mailSender.createMimeMessage();
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setTo(to);
            helper.setSubject(subject);
            helper.setText(text, false);
            mailSender.send(message);
        } catch (MessagingException e) {
            throw new RuntimeException("Failed to send email", e);
        }
    }

    public Optional<User> findByEmail(String email){
        return Optional.ofNullable(userRepository.findByEmail(email));
    }

    public User registerUser(User user){
        if(user.getPassword() != null)
            user.setPassword(bCryptEncoder.encode(user.getPassword()));
        return userRepository.save(user);
    }

    public GoogleAuthenticatorKey generate2FASecret(String userId){
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        GoogleAuthenticatorKey key = totpService.generateSecret();
        user.setTwoFactorSecret(key.getKey());
        userRepository.save(user);
        return key;
    }

    public boolean validate2FACode(String userId, int code){
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        return totpService.verifyCode(user.getTwoFactorSecret(), code);
    }

    public void enable2FA(String userId){
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        user.setTwoFactorEnabled(true);
        userRepository.save(user);
    }

    public void disable2FA(String userId){
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("User not found"));
        user.setTwoFactorEnabled(false);
        userRepository.save(user);
    }
}