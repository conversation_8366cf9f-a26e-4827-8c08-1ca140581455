package com.ensar.request.dto;

import com.ensar.entity.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

@Schema(description = "Parameters required to create/update LinkedIn profile")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateLinkedinDto {

    @Schema(description = "Account Name", required = true)
    @NotBlank(message = "Account Name is required")
    @Size(max = 100)
    private String accountName; // Renamed from "name"

    @Schema(description = "Email", required = true)
    @NotBlank(message = "Email is required")
    @Size(max = 100)
    private String email; // Renamed from "title"

    @Schema(description = "Password", required = true)
    @NotBlank(message = "Password is required")
    @Size(max = 100)
    private String password; // Renamed from "occupation"

    @Schema(description = "Designation", required = true)
    @NotBlank(message = "Designation is required")
    @Size(max = 255)
    private String designation; // Renamed from "headline"

    @Schema(description = "Country", required = true)
    @NotBlank(message = "Country is required")
    @Size(max = 100)
    private String country;

    @Schema(description = "Connections Count", required = true)
    @NotNull(message = "Connections Count is required")
    private Integer connectionsCount; // Added field

    @Schema(description = "Status", required = true)
    @NotBlank(message = "Status is required")
    @Size(max = 20)
    private String status;

    @Schema(description = "Handled By", required = true)
//    @NotBlank(message = "Handled By is required")
//    @Size(max = 100)
    private String handledById;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId;
}
