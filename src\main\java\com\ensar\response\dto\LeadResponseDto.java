package com.ensar.response.dto;

import com.ensar.repository.OrganizationRepository;
import lombok.Getter;
import lombok.Setter;

import java.sql.Date;
import java.util.List;

@Getter
@Setter
public class LeadResponseDto {

    private String id;
    private String firstname;
    private String lastname;
    private IndustryResponseDto industry; // Changed from String to IndustryResponseDto
    private String email;
    private String phoneNumber;
    private String status;
    private Date leaddate; // Added date field
    private String linkedin;
    private String website;
    private String region;
    private String empCount;
    private Boolean verified;
    private Boolean messageSent;
    private String comments;
    private UserResponseDto sentBy;
    private DesignationResponseDto designation;
    private OrganizationResponseDto organization;
    private List<LeadReplyResponseDto> leadReplies;
    private Boolean draftStatus; // true = Draft, false = Finalized
}
