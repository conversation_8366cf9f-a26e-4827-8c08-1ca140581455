package com.ensar.controller;

import com.ensar.entity.Organization;
import com.ensar.entity.Role;
import com.ensar.request.dto.CreateUpdateRoleDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.RoleService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "Role Management")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/roles")
@Tag(name = "Role Management") // Class-level Tag annotation

public class RoleController {

    private final RoleService roleService;

    @Autowired
    public RoleController(RoleService roleService) {
        this.roleService = roleService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<Role> getRoleById(@PathVariable String id) {
        Role role = roleService.getRoleById(id);
        return ResponseEntity.ok(role);
    }

    @GetMapping("/")
    @PreAuthorize("hasAnyRole('ROLE_SUPER_ADMIN', 'ROLE_ADMIN')")
    public ResponseEntity<Map<String, Page<Role>>> getAllRoles(@AuthenticationPrincipal EnsarUserDetails userDetails,
                                                               @RequestParam(defaultValue = "0") int page,
                                                               @RequestParam(defaultValue = "10") int size) {
        String orgId = userDetails.getOrganization().getId();
        Page<Role> roleList = roleService.getAllRoles(orgId,page, size);
        Map<String, Page<Role>> response = new HashMap<>();
        response.put("roles", roleList);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    public ResponseEntity<Role> createRole(@Valid @RequestBody CreateUpdateRoleDto createUpdateRoleDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Role role = roleService.createOrUpdateRole(Optional.empty(), createUpdateRoleDto,organization);
        return ResponseEntity.ok(role);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Role> updateRole(@PathVariable String id,
                                           @Valid @RequestBody CreateUpdateRoleDto createUpdateRoleDto,
                                           @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Role role = roleService.createOrUpdateRole(Optional.of(id), createUpdateRoleDto,organization);
        return ResponseEntity.ok(role);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRole(@PathVariable String id) {
        roleService.deleteRole(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/import")
    public ResponseEntity<Void> importRoles(@RequestParam("file") MultipartFile file) throws IOException {
        roleService.importRoles(file.getInputStream());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportRoles() throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        roleService.exportRoles(outputStream);
        byte[] bytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename("roles.csv")
                .build();
        headers.setContentDisposition(contentDisposition);

        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);
    }
}
