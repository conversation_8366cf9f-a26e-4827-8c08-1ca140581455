package com.ensar.repository;

import com.ensar.entity.Industry;
import com.ensar.entity.Project;
import com.ensar.entity.User;
import com.ensar.response.dto.IndustryResponseDto;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface IndustryRepository extends JpaRepository<Industry, String> {

	List<Industry> findByOrganizationId(String organizationId);

	Optional<Industry> findByName(String name);

	Industry findByNameIgnoreCase(String name);
}
