package com.ensar.controller;

import com.ensar.entity.CmsMail;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateCmsMailDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.CmsMailService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/cms-mails")
@Tag(name = "CmsMail Management") 
public class CmsMailController {

    private final CmsMailService cmsMailService;

    @Autowired
    public CmsMailController(CmsMailService cmsMailService) {
        this.cmsMailService = cmsMailService;
    }

    // Get CmsMail by ID
    @GetMapping("/{id}")
    public ResponseEntity<CmsMail> getCmsMailById(@PathVariable String id) {
        CmsMail cmsMail = cmsMailService.getCmsMailById(id);
        return ResponseEntity.ok(cmsMail);
    }

    // Get all CmsMails
    @GetMapping("/")
    public ResponseEntity<Map<String, List<CmsMail>>> getAllCmsMail(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<CmsMail> cmsMailList = cmsMailService.getAllCmsMail(orgId);
        Map<String, List<CmsMail>> response = new HashMap<>();
        response.put("cmsMail", cmsMailList);
        return ResponseEntity.ok(response);
    }

    // Create new CmsMail
    @PostMapping("/")
    public ResponseEntity<CmsMail> createCmsMail(@Valid @RequestBody CreateUpdateCmsMailDto createUpdateCmsMailDto,
                                                 @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        CmsMail cmsMail = cmsMailService.createOrUpdateCmsMail(Optional.empty(), createUpdateCmsMailDto,organization);
        return ResponseEntity.ok(cmsMail);
    }

    // Update an existing CmsMail
    @PutMapping("/{id}")
    public ResponseEntity<CmsMail> updateCmsMail(@PathVariable String id,
                                                 @Valid @RequestBody CreateUpdateCmsMailDto createUpdateCmsMailDto,
                                                 @AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        CmsMail cmsMail = cmsMailService.createOrUpdateCmsMail(Optional.of(id), createUpdateCmsMailDto,organization);
        return ResponseEntity.ok(cmsMail);
    }

    // Delete CmsMail by ID
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCmsMail(@PathVariable String id) {
        cmsMailService.deleteCmsMail(id);
        return ResponseEntity.ok().build();
    }

    // Import CmsMails from CSV file
    @PostMapping("/import")
    public ResponseEntity<Void> importCmsMail(@RequestParam("file") MultipartFile file) throws IOException {
        cmsMailService.importCmsMail(file.getInputStream());
        return ResponseEntity.ok().build();
    }

    // Export CmsMails to CSV file
    @GetMapping("/export")
    public ResponseEntity<byte[]> exportCmsMail() throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        cmsMailService.exportCmsMail(outputStream);
        byte[] bytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename("cms_mails.csv")
                .build();
        headers.setContentDisposition(contentDisposition);

        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);
    }
}
