package com.ensar.request.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class DealStatsDto {
    private BigDecimal totalPipelineValue;
    private BigDecimal weightedPipelineValue;
    private DealsWonThisMonth dealsWonThisMonth;
    private AvgDealSize avgDealSize;
    private long totalDeals;

    @Data
    public static class DealsWonThisMonth {
        private long count;
        private BigDecimal value;
    }

    @Data
    public static class AvgDealSize {
        private BigDecimal value;
        private double percentChange;
    }
}
