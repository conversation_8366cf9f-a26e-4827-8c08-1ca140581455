import pandas as pd
from sqlalchemy import create_engine, MetaData, Table, select, update

# Database connection setup for MySQL
DATABASE_URI = 'mysql+pymysql://root:root@localhost:3306/crm'
engine = create_engine(DATABASE_URI)
connection = engine.connect()
metadata = MetaData()

# Load the Excel file and sheet
file_path = 'June Email - 2024.xlsx'
df = pd.read_excel(file_path, sheet_name='Bhakta')

# Define the leads and industries tables with autoload
industries_table = Table('industries', metadata, autoload_with=engine)
leads_table = Table('leads', metadata, autoload_with=engine)

# Fetch industry IDs from the industries table and create an industry name to ID mapping
industry_mapping = {}
query = select(industries_table.c.id, industries_table.c.name)
result = connection.execute(query).mappings()

for row in result:
    industry_mapping[row['name']] = row['id']

# Debug: Print industry mapping to verify
print("Industry Mapping:", industry_mapping)

# Begin a transaction
with engine.begin() as connection:  # Ensures transaction is committed at the end
    # Iterate over each lead in the Excel file and update the industry ID in the leads table
    for _, row in df.iterrows():
        industry_name = row['Industry']
        industry_id = industry_mapping.get(industry_name)
        
        # Debug: Print each lead's data before update
        print(f"Lead Email: {row['Email ID']}, Industry Name: {industry_name}, Industry ID: {industry_id}")
        
        # Update only if there is a matching industry_id
        if industry_id:
            update_query = (
                update(leads_table)
                .where(leads_table.c.email == row['Email ID'])
                .values(industry_id=industry_id)
            )
            connection.execute(update_query)
        else:
            print(f"No match found for industry '{industry_name}'.")

# Connection is closed automatically after the transaction completes
