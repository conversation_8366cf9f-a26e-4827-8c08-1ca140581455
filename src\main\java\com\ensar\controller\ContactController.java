package com.ensar.controller;

import com.ensar.entity.Contact;
import com.ensar.request.dto.CreateUpdateContactDto;
import com.ensar.service.ContactService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

//@Api(tags = "Contact Management")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/contacts")
@Tag(name = "Contact Management") // Class-level Tag annotation

public class ContactController {

    private final ContactService contactService;

    @Autowired
    public ContactController(ContactService contactService) {
        this.contactService = contactService;
    }

    @Operation(summary= "Get contact by ID")
    @GetMapping("/{id}")
    public ResponseEntity<Contact> getContactById(
            @Parameter(description = "ID of the contact to retrieve", required = true)
            @PathVariable String id) {
        Contact contact = contactService.getContactById(id);
        return ResponseEntity.ok(contact);
    }

    @Operation(summary = "Get all contacts")
    @GetMapping("/")
    public ResponseEntity<List<Contact>> getAllContacts() {
        List<Contact> contacts = contactService.getAllContacts();
        return ResponseEntity.ok(contacts);
    }

    @Operation(summary = "Create a new contact")
    @PostMapping("/")
    public ResponseEntity<Contact> createContact(
            @Valid @RequestBody CreateUpdateContactDto contactDto) {
        Contact savedContact = contactService.createOrUpdateContact(Optional.empty(), contactDto);
        return ResponseEntity.ok(savedContact);
    }

    @Operation(summary = "Update an existing contact")
    @PutMapping("/{id}")
    public ResponseEntity<Contact> updateContact(
            @Parameter(description = "ID of the contact to update", required = true)
            @PathVariable String id,
            @Valid @RequestBody CreateUpdateContactDto contactDto) {
        Contact updatedContact = contactService.createOrUpdateContact(Optional.of(id), contactDto);
        return ResponseEntity.ok(updatedContact);
    }

    @Operation(summary = "Delete a contact by ID")
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteContact(
            @Parameter(description = "ID of the contact to delete", required = true)
            @PathVariable String id) {
        contactService.deleteContact(id);
        return ResponseEntity.ok().build();
    }
}
