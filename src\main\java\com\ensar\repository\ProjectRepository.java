package com.ensar.repository;


import com.ensar.entity.Organization;
import com.ensar.entity.Project;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;
import java.util.List;

@Repository
public interface ProjectRepository extends JpaRepository<Project, String> {

    Project findByName(String name);

    boolean existsByName(String name);

    List<Project> findByDescriptionContaining(String keyword);

    Page<Project> findByOrganizationId(String organizationId, Pageable pageable);

    List<Project> findByOrganizationId(String organizationId);

}

