package com.ensar.service;

import com.warrenstrange.googleauth.GoogleAuthenticator;
import com.warrenstrange.googleauth.GoogleAuthenticatorKey;
import com.warrenstrange.googleauth.GoogleAuthenticatorQRGenerator;
import org.springframework.stereotype.Service;

@Service
public class TotpService {

    private final GoogleAuthenticator gAuth;

    public TotpService(GoogleAuthenticator gAuth) {
        this.gAuth = gAuth;
    }

    public TotpService() {
        this.gAuth = new GoogleAuthenticator();
    }

    public GoogleAuthenticatorKey generateSecret(){
        return gAuth.createCredentials();
    }

    public String generateQrCodeUrl(GoogleAuthenticatorKey secret, String email){
        return GoogleAuthenticatorQRGenerator.getOtpAuthURL("Ensar CRM", email, secret);
    }

    public Boolean verifyCode(String secret, int code){
        return gAuth.authorize(secret, code);
    }
}
