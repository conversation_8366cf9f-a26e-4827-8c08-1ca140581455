-- Generate UUIDs for organization
SET @ORG_ID = (SELECT id FROM organization LIMIT 1);

SET @USER_ID_KALYAN = uuid();
SET @USER_ID_SRIDHAR = uuid();

INSERT INTO `user`
(id, first_name, last_name, email, role_name, organization_id)
VALUES
(@USER_ID_KALYAN, 'Kalyan', 'K', '<EMAIL>', 'ROLE_SUPER_ADMIN', @ORG_ID),
(@USER_ID_SRIDHAR, 'Sridhar', 'P', '<EMAIL>', 'ROLE_SUPER_ADMIN', @ORG_ID);

-- Generate UUIDs for industries
SET @INDUSTRY_IT_ID = uuid();
SET @INDUSTRY_AUTO_ID = uuid();

INSERT INTO industries (id, name)
VALUES
(@INDUSTRY_IT_ID, 'IT'),
(@INDUSTRY_AUTO_ID, 'Auto');

-- Generate UUIDs for companies
SET @COMPANY_ID_JOHN = uuid();
SET @COMPANY_ID_MIKE = uuid();
SET @COMPANY_ID_NANCY = uuid();

INSERT INTO companies (id, name, industry_id, website, region, empcount)
VALUES
(@COMPANY_ID_JOHN, 'John\'s IT Company', @INDUSTRY_IT_ID, 'https://www.john.com', 'North America', 100),
(@COMPANY_ID_MIKE, 'Mike\'s Auto Company', @INDUSTRY_AUTO_ID, 'https://www.mike.com', 'Europe', 200),
(@COMPANY_ID_NANCY, 'Nancy\'s IT Company', @INDUSTRY_IT_ID, 'https://www.nancy.com', 'Asia', 150);

-- Get Bhakta's UUID
SET @BHAKTA_UUID = (SELECT id FROM `user` WHERE email = '<EMAIL>' LIMIT 1);

-- Generate lead UUIDs
SET @LEAD_ID_1 = uuid();
SET @LEAD_ID_2 = uuid();
SET @LEAD_ID_3 = uuid();

-- Insert initial data into leads table
INSERT INTO leads 
(id, name, email, phonenumber, status, created_date_time, last_updated_date_time, linkedin, website, region, empcount, verified, messagesent, comments, sentby, company_id, industry_id) 
VALUES 
(@LEAD_ID_1, 'John', '<EMAIL>', '************', 'New', CURRENT_TIMESTAMP, NULL, 'https://www.linkedin.com/in/john', 'https://www.john.com', 'North America', 100, FALSE, FALSE, 'Initial lead', @BHAKTA_UUID, @COMPANY_ID_JOHN, @INDUSTRY_IT_ID),
(@LEAD_ID_2, 'Mike', '<EMAIL>', '************', 'New', CURRENT_TIMESTAMP, NULL, 'https://www.linkedin.com/in/mike', 'https://www.mike.com', 'Europe', 200, FALSE, FALSE, 'Initial lead', @BHAKTA_UUID, @COMPANY_ID_MIKE, @INDUSTRY_AUTO_ID),
(@LEAD_ID_3, 'Nancy', '<EMAIL>', '************', 'New', CURRENT_TIMESTAMP, NULL, 'https://www.linkedin.com/in/nancy', 'https://www.nancy.com', 'Asia', 150, FALSE, FALSE, 'Initial lead', @BHAKTA_UUID, @COMPANY_ID_NANCY, @INDUSTRY_IT_ID);

-- Generate leadreply UUIDs
SET @LEADRESPONSE_ID_1 = uuid();
SET @LEADRESPONSE_ID_2 = uuid();
SET @LEADRESPONSE_ID_3 = uuid();  

-- Insert initial data into leadreplies table
INSERT INTO leadreplies 
(id, lead_id, reply_text, replier_id, created_date_time, last_updated_date_time) 
VALUES 
(@LEADRESPONSE_ID_1, @LEAD_ID_1, 'Response to John', @BHAKTA_UUID, CURRENT_TIMESTAMP, NULL),
(@LEADRESPONSE_ID_2, @LEAD_ID_2, 'Response to Mike', @BHAKTA_UUID, CURRENT_TIMESTAMP, NULL),
(@LEADRESPONSE_ID_3, @LEAD_ID_3, 'Response to Nancy', @BHAKTA_UUID, CURRENT_TIMESTAMP, NULL);
