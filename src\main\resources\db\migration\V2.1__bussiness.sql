CREATE TABLE business (
    id                   CHAR(36),
    s_no                   INT NOT NULL,
    company              VARCHAR(100) NOT NULL,
    address              VARCHAR(255),
    website              VARCHAR(255),
    phone_no             VARCHAR(15),
    typeofsearch         VARCHAR(100),
    comment              TEXT,
    ecommerce            BOOLEAN DEFAULT 0,
    city            VARCHAR(50),
    organization_id CHAR(36) NOT NULL,
    created_date_time    TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP NULL DEFAULT NULL,
    CONSTRAINT fk_business_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    PRIMARY KEY (id)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8;
