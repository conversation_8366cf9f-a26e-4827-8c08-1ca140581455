package com.ensar.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.persistence.*;
import java.sql.Date;

@Entity(name = "contact")
@Data
@EqualsAndHashCode(callSuper = true)
public class Contact extends BaseEntity {

    @Column(name = "first_name")
	private String firstName;

	@Column(name = "last_name")
	private String lastName;
 
	@Column(name = "email")
	private String email;
 
	@Column(name = "enrollment_date")
	private Date enrollmentDate;
 
    @Column(name = "disabled")
	private boolean disabled = false;

}
