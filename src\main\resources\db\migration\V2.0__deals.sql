-- Drop the deal table if it already exists
DROP TABLE IF EXISTS `deals`;

-- Create the deal table
CREATE TABLE `deals`
(
    `id`                    CHAR(36)      NOT NULL,  -- Assuming UUID is used for id
    `name`                  VA<PERSON>HA<PERSON>(255)  NOT NULL,
    `lead_id`               CHAR(36)      NOT NULL,  -- Foreign key to leads table
    `email`                 VARCHAR(255)  NOT NULL,
    `stage`                 ENUM('NEW', 'NEGOTIATION', 'PROSPECTING', 'PROPOSAL', 'CLOSED_WON', 'CLOSED_LOST') NOT NULL,
    `value`                 DECIMAL(15, 2) NOT NULL,
    `expected_close_date`    DATE          NOT NULL,
    `actual_close_date`      DATE          DEFAULT NULL,
    `status`                VARCHAR(50)   NOT NULL,
    `priority`              ENUM('LOW', 'MEDIUM', 'HIGH') NOT NULL,
    `source`                ENUM('REFERRAL', 'WEBSITE', 'COLD_CALL') DEFAULT NULL,
    `next_step`             VARCHAR(255)  DEFAULT NULL,
    `notes`                 TEXT          DEFAULT NULL,
    `organization_id` CHAR(36) NOT NULL,
    `created_date_time`     TIMESTAMP     NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` TIMESTAMP    NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT fk_deal_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    FOREIGN KEY (`lead_id`) REFERENCES `leads`(`id`) ON DELETE CASCADE
) ENGINE = InnoDB
DEFAULT CHARSET = utf8;
