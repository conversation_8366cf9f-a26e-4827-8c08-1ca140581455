import pandas as pd
import mysql.connector
from mysql.connector import <PERSON><PERSON>r
import uuid

# Load Excel data with cleaned column names
file_path = 'Shops_and_related_websites_in_Illinois2.xlsx'
excel_data = pd.read_excel(file_path, skiprows=1,)
excel_data.columns = [
    's_no', 'company', 'address', 'website', 'phone_no', 'typeofsearch', 'comment', 'ecommerce'
]

# Function to extract city name (e.g., Algonquin) from the address
def extract_city_name(address):
    if pd.isna(address):
        return None
    # Split the address by commas
    parts = str(address).split(',')
    if len(parts) >= 3:  # Ensure there are enough parts to extract city
        return parts[-3].strip()  # Extract the second-to-last part as city
    return None

# Add `city` column with extracted city names
excel_data['city'] = excel_data['address'].apply(extract_city_name)

# MySQL database connection details
host = 'localhost'
user = 'root'
password = 'root'
database = 'crm'

# Define the insert query with additional `city` column
insert_query = """
INSERT INTO business
(id, s_no, company, address, website, phone_no, typeofsearch, comment, ecommerce, city)
VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
"""

# Helper function to convert `ecommerce` column to boolean
def convert_to_bool(value):
    return str(value).strip().lower() in ['yes', 'true', '1']

# Preprocess 'ecommerce' values
excel_data['ecommerce'] = excel_data['ecommerce'].apply(convert_to_bool)

# Establish a connection to the MySQL database
try:
    connection = mysql.connector.connect(
        host=host,
        user=user,
        password=password,
        database=database
    )

    if connection.is_connected():
        cursor = connection.cursor()

        # Insert each row from the data into the `business` table with UUID for `id`
        success_count, fail_count = 0, 0
        for _, row in excel_data.iterrows():
            # Generate a UUID for id
            id_uuid = str(uuid.uuid4())
            print(f"Inserting row for company: {row['company']} with UUID: {id_uuid}")  # Debug statement
            try:
                cursor.execute(insert_query, (
                    id_uuid,                 # Use UUID as the id
                    row['s_no'],             # Original s_no column value
                    row['company'],
                    row['address'],
                    row['website'],
                    row['phone_no'],
                    row['typeofsearch'],
                    row['comment'],
                    row['ecommerce'],
                    row['city']              # New `city` column
                ))
                success_count += 1
            except Error as e:
                print(f"Failed to insert row: {row['company']}, Error: {e}")
                fail_count += 1

        # Commit transaction
        connection.commit()
        print(f"Data insertion completed with {success_count} successful and {fail_count} failed inserts.")

except Error as e:
    print("Error while connecting to MySQL:", e)
finally:
    if connection.is_connected():
        cursor.close()
        connection.close()
        print("MySQL connection is closed")
