import pandas as pd
import mysql.connector
from mysql.connector import <PERSON><PERSON>r
import uuid

# Load the Excel file
file_path = 'June Email - 2024.xlsx'
excel_data = pd.ExcelFile(file_path)

# MySQL database connection details
host = 'localhost'
user = 'root'
password = 'root'
database = 'crm'

# Organization and role details
org_name = 'Demo Org'
org_description = 'Demo desc'
org_domain = 'demo-domain'

try:
    # Establish connection
    connection = mysql.connector.connect(
        host=host,
        user=user,
        password=password,
        database=database
    )
    if connection.is_connected():
        
        # Check if organization exists
        cursor = connection.cursor()
        cursor.execute("SELECT id FROM organization WHERE name = %s", (org_name,))
        org_record = cursor.fetchall()  # Fetch all results to avoid unread results
        cursor.close()  # Close cursor after fetching

        if org_record:
            org_id = org_record[0][0]
        else:
            # Insert new organization if it does not exist
            org_id = str(uuid.uuid4())
            cursor = connection.cursor()
            cursor.execute("INSERT INTO organization (id, name, description, domain) VALUES (%s, %s, %s, %s)",
                           (org_id, org_name, org_description, org_domain))
            connection.commit()  # Commit organization insert
            cursor.close()

        # Check if ROLE_SUPER_ADMIN exists
        cursor = connection.cursor()
        cursor.execute("SELECT id FROM role WHERE role_name = 'ROLE_SUPER_ADMIN'")
        role_record = cursor.fetchall()  # Fetch all results
        cursor.close()  # Close cursor after fetching

        if role_record:
            role_super_admin_id = role_record[0][0]
        else:
            # Insert roles if they don't exist and set ROLE_SUPER_ADMIN as default
            role_super_admin_id = str(uuid.uuid4())
            role_admin_id = str(uuid.uuid4())
            role_user_id = str(uuid.uuid4())
            
            cursor = connection.cursor()
            cursor.execute("""
                INSERT INTO role (id, role_name, role_description, role_permission) VALUES
                (%s, 'ROLE_SUPER_ADMIN', 'Super Administrator', 'ALL_PERMISSIONS'),
                (%s, 'ROLE_ADMIN', 'Administrator', 'ADMIN_PERMISSIONS'),
                (%s, 'ROLE_USER', 'Regular User', 'USER_PERMISSIONS')
            """, (role_super_admin_id, role_admin_id, role_user_id))
            connection.commit()  # Commit roles insert
            cursor.close()

        # Insert users based on each sheet in Excel (sheet name as first_name)
        for sheet_name in excel_data.sheet_names:
            user_data = pd.read_excel(excel_data, sheet_name=sheet_name)
            
            # Assuming last name and email columns exist in each sheet
            for index, row in user_data.iterrows():
                user_id = str(uuid.uuid4())
                first_name = sheet_name
                last_name = row.get('last_name', 'Unknown')  # Default if 'last_name' is missing
                email = row.get('email', f'{first_name.lower()}@example.com')  # Default email if missing

                # Insert or update user with ROLE_SUPER_ADMIN_ID and organization ID
                cursor = connection.cursor()
                cursor.execute("""
                    INSERT INTO user (id, first_name, last_name, email, role_id, organization_id) 
                    VALUES (%s, %s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                        first_name = VALUES(first_name),
                        last_name = VALUES(last_name),
                        role_id = VALUES(role_id),
                        organization_id = VALUES(organization_id)
                """, (user_id, first_name, last_name, email, role_super_admin_id, org_id))
                connection.commit()  # Commit each user insert or update
                cursor.close()

        print("Organization, roles, and users inserted successfully.")

except Error as e:
    print("Error while connecting to MySQL:", e)
finally:
    if connection.is_connected():
        connection.close()
        print("MySQL connection is closed")
