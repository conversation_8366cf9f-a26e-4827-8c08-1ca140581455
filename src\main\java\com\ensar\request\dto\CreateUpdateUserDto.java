package com.ensar.request.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

@Schema(description ="Parameters required to create/update user")
@Accessors(chain = true)
@Setter
@Getter
@JsonIgnoreProperties(ignoreUnknown = true)
public class CreateUpdateUserDto {

    @Schema(description = "User Email", required = true)
    @NotBlank(message = "User Email is required")
    @Email
    private String email;

    @Schema(description = "User password", required = false)
    private String password;

    @Schema(description = "User First Name", required = true)
    @NotBlank(message = "User First Name is required")
    @Size(max = 50)
    private String firstName;

    @Schema(description = "User Status")
    private String status;

    @Schema(description = "User Last Name", required = true)
    @Size(max = 50)
    private String lastName;

    @Schema(description = "Sign-up Method")
    private String signUpMethod;

    @Schema(description = "Two Factor Secret")
    private String twoFactorSecret;

    @Schema(description = "is 2fa enabled")
    private String isTwoFactorEnabled;

    @Schema(description = "User Role ID", required = true)
    @NotBlank(message = "User Role ID is required")
    private String roleId;

    @Schema(description = "City")
    @Size(max = 50)
    private String city;

    @Schema(description = "State")
    @Size(max = 50)
    private String state;

    @Schema(description = "Country")
    @Size(max = 50)
    private String country;

    @Schema(description =  "Address")
    private String address;

    @Schema(description = "Zip Code")
    private String zipCode;

    @Schema(description = "Phone Number")
    private String phoneNumber;

    @Schema(description = "Company")
    private String company;

    @Schema(description = "isVerified")
    private boolean verified = false; // Fixed the capitalization

    @Schema(description = "emailVerified")
    private boolean emailVerified = false;

    @Schema(description = "disabled")
    private boolean disabled = false;

    @Schema(description = "Avatar URL")
    private String avatarUrl;

    @Schema(description = "User Organization ID", required = true)
//    @NotBlank(message = "Organization ID is required")
    private String organizationId; // Keeping organizationId as a String to reference by ID
}
