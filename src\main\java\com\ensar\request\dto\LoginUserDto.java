package com.ensar.request.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

@Schema(description = "Parameters required to login user")
@Accessors(chain = true)
@Setter
@Getter
public class LoginUserDto {
    @Schema(description = "User Email", required = true)
    @NotBlank(message = "User Email is required")
    private String email;

    @Schema(description = "User password", required = true)
    @NotBlank(message = "Password is required")
    private String password;
}
