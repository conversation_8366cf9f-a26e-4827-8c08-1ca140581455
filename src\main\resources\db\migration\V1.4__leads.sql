-- Drop existing tables if they exist
DROP TABLE IF EXISTS lead_replies;
CREATE TABLE leads
(
    id                    CHAR(36)    NOT NULL,
    first_name            VARCHAR(50) NOT NULL,
    last_name             VARCHAR(50) NOT NULL,
    designation_id        VARCHAR(50) NOT NULL,
    email                 VARCHAR(50) NULL,
    lead_date             TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    phone_number           VARCHAR(25) NULL,
    status                ENUM('New', 'Contacted', 'Qualified', 'Lost', 'Won', 'Unqualified')  DEFAULT 'New',
    linkedin              VARCHAR(255) NULL,
    website               Text DEFAULT NULL,
    region                VARCHAR(50),
    emp_count              VARCHAR(15),
    verified              BOOLEAN NOT NULL DEFAULT FALSE,
    message_sent          BOOLEAN NOT NULL DEFAULT FALSE,
    comments              TEXT,
    company_id           CHAR(36) NULL,
    industry_id          CHAR(36) NULL,
    sent_by                <PERSON><PERSON>(36) NOT NULL,
    draft_status TINYINT(1) NOT NULL DEFAULT 0,
    organization_id CHAR(36) NOT NULL,
    created_date_time     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP  NULL DEFAULT NULL,

    -- Generated columns for enforcing unique constraints on non-null and non-empty values
    unique_phone_number    VARCHAR(25) GENERATED ALWAYS AS (IF(phone_number IS NULL OR phone_number = '', NULL, phone_number)) VIRTUAL,
    unique_email          VARCHAR(50) GENERATED ALWAYS AS (IF(email IS NULL OR email = '', NULL, email)) VIRTUAL,
    unique_linkedin       VARCHAR(255) GENERATED ALWAYS AS (IF(linkedin IS NULL OR linkedin = '', NULL, linkedin)) VIRTUAL,
    CONSTRAINT fk_leads_sent_by FOREIGN KEY (sent_by) REFERENCES users(id) ON DELETE CASCADE,
    CONSTRAINT fk_leads_company_id FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    CONSTRAINT fk_leads_industry_id FOREIGN KEY (industry_id) REFERENCES industries(id) ON DELETE CASCADE,
    CONSTRAINT fk_leads_organization_id FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE,
    CONSTRAINT fk_leads_designation_id FOREIGN KEY (designation_id) REFERENCES designations(id) ON DELETE CASCADE,
    PRIMARY KEY (id)
) ENGINE = InnoDB
DEFAULT CHARSET = utf8;

-- Create unique indexes on generated columns to enforce uniqueness for non-null and non-empty values
CREATE UNIQUE INDEX unique_phone_number_idx ON leads (unique_phone_number);
CREATE UNIQUE INDEX unique_email_idx ON leads (unique_email);
CREATE UNIQUE INDEX unique_linkedin_idx ON leads (unique_linkedin);



CREATE TABLE lead_replies
(
    id            CHAR(36) NOT NULL,
    lead_id       CHAR(36) NOT NULL REFERENCES leads(id),
    reply_text    TEXT     NOT NULL,
    replier_id    CHAR(36) NOT NULL REFERENCES `users`(`id`),
    reply_at            DATE  NULL  ,
    organization_id CHAR(36) NOT NULL,
    created_date_time     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP  NULL DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_lead_replies_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB
DEFAULT CHARSET = utf8;


CREATE TABLE lead_responses
(
    id           CHAR(36) NOT NULL,
    reply_id              CHAR(36) NOT NULL REFERENCES lead_replies(id),
    response              TEXT NOT NULL,
    respond_at            DATE  NOT NULL,
    organization_id CHAR(36) NOT NULL,
    created_date_time     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP  NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT fk_lead_responses_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB
DEFAULT CHARSET = utf8;



CREATE TABLE lead_tasks
(
    id                    CHAR(36)    NOT NULL,
    task_name               VARCHAR(30) NOT NULL,
    priority                ENUM('Low', 'Medium', 'High') NOT NULL DEFAULT 'Medium',
    lead_id               CHAR(36)    NOT NULL REFERENCES leads(id),
    task_description      TEXT        NOT NULL,
    assigned_to           CHAR(36)    NOT NULL REFERENCES `users`(`id`),
    start_date              TIMESTAMP     NOT NULL,
    end_date             TIMESTAMP          NOT NULL,
    status                ENUM('Pending', 'InProgress', 'Completed') NOT NULL DEFAULT 'Pending',
    organization_id CHAR(36) NOT NULL,
    created_date_time     TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_updated_date_time TIMESTAMP  NULL DEFAULT NULL,
    PRIMARY KEY (id),
    CONSTRAINT fk_lead_tasks_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB
DEFAULT CHARSET = utf8;

