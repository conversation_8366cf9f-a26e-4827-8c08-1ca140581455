package com.ensar.entity;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.Data;
import lombok.EqualsAndHashCode;

import jakarta.persistence.*;

import java.sql.Date;
import java.util.List;

@Entity(name = "lead_replies")
@Data
@EqualsAndHashCode(callSuper = true)
@JsonIgnoreProperties(ignoreUnknown = true)
public class LeadReply extends BaseEntity {


    @ManyToOne
    @JsonBackReference
    @JoinColumn(name = "lead_id", nullable = false)
    private Lead lead;

    @Column(name = "reply_text", nullable = false)
    private String replyText;

    @Column(name = "reply_at", nullable = false)
    private Date replyAt;

    @ManyToOne
    @JoinColumn(name = "replier_id", nullable = false)
    private User replier;

//    @OneToMany(mappedBy = "reply", cascade = CascadeType.ALL, orphanRemoval = true)
//    private List<LeadResponse> responses;


    @OneToMany(mappedBy = "reply", cascade = CascadeType.ALL, orphanRemoval = true)
    @JsonManagedReference
    private List<LeadResponse> responses;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;

}