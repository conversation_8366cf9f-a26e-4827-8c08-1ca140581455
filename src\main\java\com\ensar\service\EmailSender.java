package com.ensar.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.SendMessageRequest;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;



@Component
@Log4j2
public class EmailSender {

    @Value("${EMAIL_FROM}")
    private String fromEmailAddress;


    @Value("${SQS_QUEUE_URL}")
    private String sqsQueueUrl;

    private final SqsClient sqsClient;
    private final ObjectMapper objectMapper;  // Local ObjectMapper

    public EmailSender(SqsClient sqsClient) {
        this.sqsClient = sqsClient;
        this.objectMapper = new ObjectMapper();  // Use local ObjectMapper
    }

    public void sendSimpleMessage(String to, String subject, String messageBody, String password) {
        if (to == null || to.isEmpty()) {
            log.error("Recipient email is missing, aborting send.");
            return;
        }
        if (messageBody == null || messageBody.isEmpty()) {
            log.error("Message body is empty or null, aborting send.");
            return;
        }

        log.info("Preparing to send email to SQS. Recipient: {}, Subject: {}", to, subject);

        try {
            Map<String, String> emailDetails = new HashMap<>();
            emailDetails.put("FromEmail", fromEmailAddress);
            emailDetails.put("RecipientEmail", to);
            emailDetails.put("Subject", subject);
            emailDetails.put("MessageBody", messageBody);  // Explicitly use MessageBody

            String serializedMessage = objectMapper.writeValueAsString(emailDetails);
            log.info("SQS Message Body: {}", serializedMessage);

            String messageDeduplicationId = UUID.randomUUID().toString();

            SendMessageRequest sendMessageRequest = SendMessageRequest.builder()
                    .queueUrl(sqsQueueUrl)
                    .messageBody(serializedMessage)
                    .messageGroupId("email-group-1")
                    .messageDeduplicationId(messageDeduplicationId)
                    .build();

            sqsClient.sendMessage(sendMessageRequest);
            log.info("Email details sent to SQS for user: {}", to);
        } catch (Exception e) {
            log.error("Error sending email to SQS: ", e);
        }
    }

}