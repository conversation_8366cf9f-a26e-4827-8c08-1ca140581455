package com.ensar.entity;

import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.persistence.*;

import java.sql.Date;
import java.sql.Timestamp;

@Entity(name = "user_tasks")
@Data
@EqualsAndHashCode(callSuper = true)
public class UserTask extends BaseEntity {

    @Column(name = "name", nullable = false)
    private String name;

    @Column(name = "description", nullable = false, columnDefinition = "TEXT")
    private String description;

    @Column(name = "start_date", nullable = false)
    private Timestamp startDate;

    @Column(name = "end_date", nullable = false)
    private Timestamp endDate;

    @Column(name = "priority", nullable = false, length = 10)
    @Enumerated(EnumType.STRING)
    private Priority priority;

    @Column(name = "status", nullable = false, length = 15)
    @Enumerated(EnumType.STRING)
    private Status status;

    public enum Priority {
        Low,
        Medium,
        High
    }

    public enum Status {
        Pending,
        InProgress,
        Completed
    }
    @ManyToOne
    @JoinColumn(name = "user_id", referencedColumnName = "id", nullable = false)
    private User user;

    @ManyToOne
//    @JsonBackReference
    @JoinColumn(name = "lead_id", nullable = false)
    private Lead lead;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;
}
