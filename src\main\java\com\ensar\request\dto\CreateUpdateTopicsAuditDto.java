package com.ensar.request.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import jakarta.validation.constraints.*;

@Schema(description = "Parameters required to create/update topics audit")
@Accessors(chain = true)
@Setter
@Getter
public class CreateUpdateTopicsAuditDto {

    @Schema(description = "User ID", required = true)
    @NotBlank(message = "User ID is required")
    private String userId;

    @Schema(description = "Topic ID", required = true)
    @NotBlank(message = "Topic ID is required")
    private String topicId;

    @Schema(description = "Audit Result", required = true)
    @NotNull(message = "Result is required")
    private Result result;

    @Schema(description = "Audit Evidence")
    private String evidence;

    public enum Result { HIGH, MEDIUM, BASIC, NA, NO }
}
