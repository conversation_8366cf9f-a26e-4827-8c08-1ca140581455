package com.ensar.repository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import com.ensar.entity.Organization;
import com.ensar.entity.User;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends JpaRepository<User, String> {

    User findByEmail(String email);

    boolean existsByEmail(String email);

    Page<User> findByOrganization(Organization organization, Pageable pageable);

    Page<User> findByOrganizationId(String organizationId, Pageable pageable);

    List<User> findByOrganization(Organization organization);

    List<User> findByOrganizationId(String organizationId);

    Optional<User> findByFirstNameIgnoreCase(String name);
    Optional<User> findByEmailIgnoreCase(String email);

}
