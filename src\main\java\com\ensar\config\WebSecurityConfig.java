package com.ensar.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

import com.ensar.controller.AuthEntryPoint;
import com.ensar.controller.AuthenticationFilter;
import com.ensar.service.UserService;

import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

@Configuration
@EnableMethodSecurity
@EnableWebSecurity
public class WebSecurityConfig {

  @Autowired
  private UserService userDetailsService;

  @Autowired
  private AuthEntryPoint unauthorizedHandler;

  @Autowired
  @Lazy
  OAuth2LoginSuccessHandler oAuth2LoginSuccessHandler;

  @Bean
  public AuthenticationFilter authenticationJwtTokenFilter() {
    return new AuthenticationFilter();
  }

  @Bean
  public DaoAuthenticationProvider authenticationProvider() {
    DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
    authProvider.setUserDetailsService(userDetailsService);
    authProvider.setPasswordEncoder(passwordEncoder());
    return authProvider;
  }

  @Bean
  public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
    return authConfig.getAuthenticationManager();
  }

  @Bean
  public PasswordEncoder passwordEncoder() {
    return new BCryptPasswordEncoder();
  }

  @Bean
  public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
    http.csrf(csrf -> csrf.disable()) // Disable CSRF for simplicity; enable if needed
            .cors(cors -> cors.configurationSource(corsConfigurationSource())) // Enable CORS
            .exceptionHandling(exception -> exception.authenticationEntryPoint(unauthorizedHandler))
            .sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS)) // Use stateless sessions
            .authorizeHttpRequests(auth -> auth
                    .requestMatchers("/api/auth/**").permitAll() // Allow public access to auth APIs
                    .requestMatchers("/api/test/**").permitAll() // Allow public access to test APIs
                    .requestMatchers("/v3/api-docs/**", "/swagger-ui/**", "/swagger-ui.html", "/swagger-resources/**", "/webjars/**").permitAll()
                    .requestMatchers("/api/cms-contents/{id}/image").permitAll() // Allow public access to image URL
                    .requestMatchers("/oauth2/**").permitAll()
                    .anyRequest().authenticated())
                    .oauth2Login(oauth2->{
                      oauth2.successHandler(oAuth2LoginSuccessHandler);
                    });

    // Set the authentication provider
    http.authenticationProvider(authenticationProvider());

    // Add the JWT authentication filter
    http.addFilterBefore(authenticationJwtTokenFilter(), UsernamePasswordAuthenticationFilter.class);

    return http.build();
  }

  // Global CORS configuration
  @Bean
  public CorsFilter corsFilter() {
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    CorsConfiguration config = new CorsConfiguration();
    config.setAllowCredentials(true);  // Allow credentials such as cookies and authorization headers
    config.addAllowedOriginPattern("*");  // Allow all origins (you can restrict this to specific domains)
    config.addAllowedHeader("*");  // Allow all headers
    config.addAllowedMethod("*");  // Allow all HTTP methods (GET, POST, PUT, DELETE, etc.)
    source.registerCorsConfiguration("/**", config);
    return new CorsFilter(source);
  }

  private UrlBasedCorsConfigurationSource corsConfigurationSource() {
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    CorsConfiguration configuration = new CorsConfiguration();
    configuration.setAllowCredentials(true);  // Allow credentials
    configuration.addAllowedOriginPattern("*");  // Allow all origins (you can specify particular origins here)
    configuration.addAllowedHeader("*");  // Allow all headers
    configuration.addAllowedMethod("*");  // Allow all methods
    source.registerCorsConfiguration("/**", configuration);
    return source;
  }
}