-- Generate Organization UUID
SET @ORG_ID = uuid();
INSERT INTO organizations (id, name, description, domain)
VALUES (@ORG_ID, 'Demo Org', 'Demo desc', 'demo-domain');

-- Insert into designations first
SET @DESIGNATION_ID_1 = uuid();
SET @DESIGNATION_ID_2 = uuid();
SET @DESIGNATION_ID_3 = uuid();

INSERT INTO `designations`
(`id`, `name`, `description`, `organization_id`)
VALUES
(@DESIGNATION_ID_1, 'Software Engineer', 'Responsible for designing and developing software applications', @ORG_ID),
(@DESIGNATION_ID_2, 'Project Manager', 'Oversees project timelines, resources, and team performance', @ORG_ID),
(@DESIGNATION_ID_3, 'Business Analyst', 'Analyzes business needs and defines solutions to business problems', @ORG_ID),
(uuid(), 'Internet Marketplace Platforms', 'Responsible for providing internet marketplace platforms', @ORG_ID);


-- Retrieve the inserted Designation ID for Software Engineer
SET @DESIGNATION_ID = (SELECT id FROM designations WHERE name = 'Software Engineer' LIMIT 1);

-- Inserting into the `role` table
SET @ROLE_ID_1 = uuid();
SET @ROLE_ID_2 = uuid();
SET @ROLE_ID_3 = uuid();

INSERT INTO `roles`
(`id`, `role_name`, `role_description`, `role_permission`, `organization_id`)
VALUES
(@ROLE_ID_1, 'superAdmin', 'Has full access to all system resources', 'ROLE_SUPER_ADMIN', @ORG_ID),
(@ROLE_ID_2, 'Admin', 'Has administrative access with some restrictions', 'ROLE_ADMIN', @ORG_ID),
(@ROLE_ID_3, 'user', 'Has limited access to resources', 'ROLE_USER', @ORG_ID);

-- Generate UUIDs for users
SET @USER_ID_BHAKTA = uuid();
SET @USER_ID_HEMANTH = uuid();

INSERT INTO `users`
(id, first_name, last_name, email, password, sign_up_method,role_id, organization_id, city, state, country, address, zip_code, phone_number, company, avatar_url, status, is_verified, email_verified, disabled, created_date_time, last_updated_date_time)
VALUES(
    @USER_ID_BHAKTA,
    'Bhakta',
    'Reddy',
    '<EMAIL>',
    '$2a$10$xVRF.RqfSJcJzExzhVqFl.geliI3URbG4ZLmyz5amMwIXU2f.uG6a',
    'email',
    @ROLE_ID_1,
    @ORG_ID,
    'Hyderabad', 'Telangana', 'India', '123 Demo Street', '500081',
    '1234567890', 'Ensar Solutions', 'https://example.com/avatar.jpg',
    'active', true, true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
),
(
    @USER_ID_HEMANTH,
    'Hemanth',
    't',
    '<EMAIL>',
    '$2a$10$xVRF.RqfSJcJzExzhVqFl.geliI3URbG4ZLmyz5amMwIXU2f.uG6a',
    'email',
    @ROLE_ID_1,
    @ORG_ID,
    'Hyderabad', 'Telangana', 'India', '123 Demo Street', '500081',
    '1234567890', 'Ensar Solutions', 'https://example.com/avatar.jpg',
    'active', true, true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
),
(
    uuid(),
    'CRM',
    'Owner',
    '<EMAIL>',
    '$2a$10$xVRF.RqfSJcJzExzhVqFl.geliI3URbG4ZLmyz5amMwIXU2f.uG6a',
    'email',
    @ROLE_ID_1,
    @ORG_ID,
    'Hyderabad', 'Telangana', 'India', '123 Demo Street', '500081',
    '1234567890', 'Ensar Solutions', 'https://example.com/avatar.jpg',
    'active', true, true, false, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
);

SET @LEAD_ID_1 = uuid();
SET @LEAD_ID_2 = uuid();
SET @LEAD_ID_3 = uuid();

-- Insert into industries before leads
SET @INDUSTRY_ID_1 = uuid();
SET @INDUSTRY_ID_2 = uuid();
SET @INDUSTRY_ID_3 = uuid();

INSERT INTO `industries`
(`id`, `name`, `organization_id`)
VALUES
(@INDUSTRY_ID_1, 'IT', @ORG_ID),
(@INDUSTRY_ID_2, 'Sales', @ORG_ID),
(@INDUSTRY_ID_3, 'Non-IT', @ORG_ID);

-- Retrieve the Industry ID
SET @INDUSTRY_1 = (SELECT id FROM industries WHERE name = 'IT' LIMIT 1);
SET @USER_ID_1 = (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1);

-- Insert into leads after required data is available
INSERT INTO `leads`
(`id`, `first_name`, `last_name`, `designation_id`, `email`, `lead_date`, `phone_number`, `status`, `linkedin`, `website`, `region`, `emp_count`, `verified`, `message_sent`, `comments`, `sent_by`, `organization_id`, `industry_id`,`draft_status`)
VALUES
(@LEAD_ID_1, 'Alice', 'Johnson', @DESIGNATION_ID, '<EMAIL>', '2024-10-01', '+************', 'New', 'https://linkedin.com/in/alicejohnson', 'https://alicejohnson.com', 'North America', '51-100', FALSE, FALSE, 'Initial contact made', @USER_ID_1, @ORG_ID, @INDUSTRY_1, 0),
(@LEAD_ID_2, 'Bob', 'Smith', @DESIGNATION_ID, '<EMAIL>', '2024-10-02', '+************', 'Contacted', 'https://linkedin.com/in/bobsmith', NULL, 'Europe', '51-100', TRUE, TRUE, 'Follow-up required', @USER_ID_1, @ORG_ID, @INDUSTRY_1, 1),
(@LEAD_ID_3, 'Charlie', 'Brown', @DESIGNATION_ID, '<EMAIL>', '2024-10-03', '+************', 'Qualified', 'https://linkedin.com/in/charliebrown', 'https://charliebrown.com', 'Asia', '51-100', FALSE, FALSE, NULL, @USER_ID_1, @ORG_ID, @INDUSTRY_1, 0);

-- Ensure the `calendar_events` table has the required columns
SET @EVENT_ID_1 = uuid();
SET @EVENT_ID_2 = uuid();
SET @EVENT_ID_3 = uuid();

INSERT INTO calendar_events (id, title, description, user_id, lead_id, start_date, end_date, all_day, color, organization_id)
VALUES
(@EVENT_ID_1, 'Meeting with Team', 'Discuss project updates and new tasks', @USER_ID_BHAKTA, @LEAD_ID_1, '2024-10-03 09:00:00', '2024-10-03 10:00:00', FALSE, 'blue', @ORG_ID),
(@EVENT_ID_2, 'Doctor Appointment', 'Annual health check-up', @USER_ID_BHAKTA, @LEAD_ID_2, '2024-10-03 15:00:00', '2024-10-03 16:00:00', FALSE, 'green', @ORG_ID),
(@EVENT_ID_3, 'All Day Conference', 'Attending tech conference', @USER_ID_BHAKTA, @LEAD_ID_3, '2024-10-03 00:00:00', '2024-10-03 23:59:59', TRUE, 'purple', @ORG_ID);
-- Insert sample data into target table

SET @TARGET_ID_1 = uuid();
SET @TARGET_ID_2 = uuid();
SET @TARGET_ID_3 = uuid();
SET @TARGET_ID_4 = uuid();
SET @TARGET_ID_5 = uuid();
SET @TARGET_ID_6 = uuid();
SET @TARGET_ID_7 = uuid();
SET @TARGET_ID_8 = uuid();
SET @TARGET_ID_9 = uuid();
SET @TARGET_ID_10 = uuid();
SET @TARGET_ID_11 = uuid();
SET @TARGET_ID_12 = uuid();

SET @USER_ID = (SELECT id FROM users WHERE email = '<EMAIL>');

INSERT INTO targets
(id, account_name, connections_count, handled_by, organization_id, no_of_leads_identified, connections_sent, messages_sent, created_date, follow_ups, response_received, meetings_scheduled, in_mail_count, postings, status)
VALUES
(@TARGET_ID_1, 'Client A', 50, @USER_ID, @ORG_ID, 20, 40, 30, '2024-11-25', 9, 'YES', 5, 3, 2, 'Active'),
(@TARGET_ID_3, 'Client C', 90, @USER_ID, @ORG_ID, 40, 60, 50, '2024-11-27', 9, 'YES', 7, 5, 1, 'OnHold'),
(@TARGET_ID_2, 'Client B', 70, @USER_ID, @ORG_ID, 74, 50, 40, '2024-11-26', 9, 'YES', 6, 4, 1, 'InActive'),
(@TARGET_ID_4, 'Client D', 50, @USER_ID, @ORG_ID, 20, 40, 30, '2024-11-28', 9, 'YES', 5, 3, 1, 'Active'),
(@TARGET_ID_5, 'Client E', 58, @USER_ID, @ORG_ID, 30, 7, 40, '2024-11-29', 9, 'YES', 6, 4, 1, 'OnHold'),
(@TARGET_ID_6, 'Client F', 52, @USER_ID, @ORG_ID, 74, 60, 50, '2024-11-30', 9, 'YES', 7, 5, 1, 'InActive'),
(@TARGET_ID_7, 'Client G', 31, @USER_ID, @ORG_ID, 7, 7, 30, '2024-12-01', 9, 'YES', 5, 3, 1, 'Active'),
(@TARGET_ID_8, 'Client H', 54, @USER_ID, @ORG_ID, 8, 74, 40, '2024-12-02', 9, 'YES', 6, 4, 1, 'OnHold'),
(@TARGET_ID_9, 'Client I', 78, @USER_ID, @ORG_ID, 8, 60, 50, '2024-12-03', 9, 'YES', 7, 5, 1, 'InActive'),
(@TARGET_ID_10, 'Client J', 63, @USER_ID, @ORG_ID, 4, 40, 30, '2024-12-05', 9, 'YES', 5, 3, 1, 'Active'),
(@TARGET_ID_11, 'Client K', 25, @USER_ID, @ORG_ID, 30, 50, 40, '2024-12-06', 9, 'YES', 6, 4, 1, 'OnHold'),
(@TARGET_ID_12, 'Client L', 20, @USER_ID, @ORG_ID, 40, 60, 50, '2024-12-08', 9, 'YES', 7, 5, 1, 'InActive');

SET @LINKEDIN_ID_1 = uuid();
SET @LINKEDIN_ID_2 = uuid();
SET @LINKEDIN_ID_3 = uuid();

INSERT INTO `linkedins`
  (`id`, `account_name`, `email`, `password`, `designation`, `country`, `connections_count`, `status`, `handled_by`, `organization_id`)
VALUES
  (@LINKEDIN_ID_1, 'Client A', '<EMAIL>', 'password123', 'Business Development Manager', 'USA', 50, 'Active', @USER_ID, @ORG_ID),
  (@LINKEDIN_ID_2, 'Client B', '<EMAIL>', 'password456', 'Marketing Director', 'Canada', 120, 'In-Active', @USER_ID, @ORG_ID),
  (@LINKEDIN_ID_3, 'Client C', '<EMAIL>', 'password789', 'Product Manager', 'UK', 75, 'On-Hold', @USER_ID, @ORG_ID);

SET @PROJECT_ID_1 = uuid();
SET @PROJECT_ID_2 = uuid();
SET @PROJECT_ID_3 = uuid();

INSERT INTO `projects`
(`id`, `name`, `description`, `organization`)
VALUES
(@PROJECT_ID_1, 'Project A', 'This project is focused on developing new features for the product', @ORG_ID),
(@PROJECT_ID_2, 'Project B', 'This project aims to improve user interface and experience', @ORG_ID),
(@PROJECT_ID_3, 'Project C', 'This project handles system optimization and performance improvements', @ORG_ID);

SET @TASK_ID_1 = uuid();
SET @TASK_ID_2 = uuid();
SET @TASK_ID_3 = uuid();

INSERT INTO `user_tasks`
(`id`, `name`, `user_id`, `lead_id`, `description`, `start_date`, `end_date`, `priority`, `status`, `organization_id`)
VALUES
(@TASK_ID_1, 'Task 1', @USER_ID_BHAKTA, @LEAD_ID_1, 'Task description for Task 1', '2024-12-05', DATE_ADD('2024-12-05', INTERVAL 3 DAY), 'High', 'Pending', @ORG_ID),
(@TASK_ID_2, 'Task 2', @USER_ID_HEMANTH, @LEAD_ID_2, 'Task description for Task 2', '2024-12-06', DATE_ADD('2024-12-06', INTERVAL 5 DAY), 'Medium', 'InProgress', @ORG_ID),
(@TASK_ID_3, 'Task 3', @USER_ID_BHAKTA, @LEAD_ID_3, 'Task description for Task 3', '2024-12-06', DATE_ADD('2024-12-06', INTERVAL 7 DAY), 'Low', 'Completed', @ORG_ID);

SET @CONTENT_ID_1 = uuid();
SET @CONTENT_ID_2 = uuid();
SET @CONTENT_ID_3 = uuid();

INSERT INTO `cms_contents`
(`id`, `title`, `description`, `content`, `meta_title`, `meta_tags`, `meta_description`, `meta_keywords`, `cover_url`, `cover_url_data`, `organization_id`)
VALUES
(@CONTENT_ID_1, 'Content Title 1', 'Description for content 1', 'This is the content of the first page.', 'Meta Title 1', 'tag1,tag2', 'Meta description for content 1', 'keyword1,keyword2', 'http://example.com/cover1.jpg', NULL, @ORG_ID),
(@CONTENT_ID_2, 'Content Title 2', 'Description for content 2', 'This is the content of the second page.', 'Meta Title 2', 'tag3,tag4', 'Meta description for content 2', 'keyword3,keyword4', 'http://example.com/cover2.jpg', NULL, @ORG_ID),
(@CONTENT_ID_3, 'Content Title 3', 'Description for content 3', 'This is the content of the third page.', 'Meta Title 3', 'tag5,tag6', 'Meta description for content 3', 'keyword5,keyword6', 'http://example.com/cover3.jpg', NULL, @ORG_ID);

SET @MAIL_ID_1 = uuid();
SET @MAIL_ID_2 = uuid();
SET @MAIL_ID_3 = uuid();

INSERT INTO `cms_mails`
(`id`, `subject`, `content`, `organization_id`)
VALUES
(@MAIL_ID_1, 'Welcome to the Organization', 'This is the content of the welcome email.', @ORG_ID),
(@MAIL_ID_2, 'Monthly Newsletter', 'This is the content of the monthly newsletter.', @ORG_ID),
(@MAIL_ID_3, 'Important Update', 'This is the content of an important update email.', @ORG_ID);


INSERT INTO deals (
  id,
  name,
  lead_id,
  email,
  stage,
  value,
  expected_close_date,
  actual_close_date,
  status,
  priority,
  source,
  next_step,
  notes,
  organization_id
) VALUES
(
  uuid(),
  'International Expansion Deal',
  @LEAD_ID_1,
  '<EMAIL>',
  'PROSPECTING',
  180000.00,
  '2025-07-20',
  NULL,
  'ACTIVE',
  'HIGH',
  'REFERRAL',
  'Prepare pricing proposal',
  'Interested in international markets',
  @ORG_ID
),
(
  uuid(),
  'Healthcare Software Proposal',
  @LEAD_ID_1,
  '<EMAIL>',
  'NEGOTIATION',
  95000.00,
  '2025-06-25',
  NULL,
  'ACTIVE',
  'MEDIUM',
  'WEBSITE',
  'Send detailed demo video',
  'Looking for HIPAA compliant solution',
  @ORG_ID
),
(
  uuid(),
  'Retail Client Negotiation',
  @LEAD_ID_1,
  '<EMAIL>',
  'PROPOSAL',
  125000.00,
  '2025-08-10',
  NULL,
  'ACTIVE',
  'HIGH',
  'COLD_CALL',
  'Schedule final review meeting',
  'Negotiating pricing terms',
  @ORG_ID
),
(
  uuid(),
  'Government Contract Won',
  @LEAD_ID_1,
  '<EMAIL>',
  'CLOSED_WON',
  250000.00,
  '2025-05-22',
  '2025-05-18',
  'CLOSED',
  'HIGH',
  'REFERRAL',
  NULL,
  'Contract awarded after RFP process',
  @ORG_ID
),
(
  uuid(),
  'Tech Startup Lost',
  @LEAD_ID_1,
  '<EMAIL>',
  'CLOSED_LOST',
  60000.00,
  '2025-05-05',
  '2025-05-10',
  'CLOSED',
  'MEDIUM',
  'WEBSITE',
  NULL,
  'Client chose competitor solution',
  @ORG_ID
),
(
  uuid(),
  'Automotive Industry Deal',
  @LEAD_ID_1,
  '<EMAIL>',
  'PROSPECTING',
  130000.00,
  '2025-07-01',
  NULL,
  'ACTIVE',
  'MEDIUM',
  'REFERRAL',
  'Arrange on-site demo',
  'Interested in supply chain optimization',
  @ORG_ID
),
(
  uuid(),
  'Education Sector Proposal',
  @LEAD_ID_1,
  '<EMAIL>',
  'NEW',
  80000.00,
  '2025-06-15',
  NULL,
  'ACTIVE',
  'LOW',
  'WEBSITE',
  'Schedule Q&A session',
  'School district interested in LMS',
  @ORG_ID
),
(
  uuid(),
  'Media Company Negotiation',
  @LEAD_ID_1,
  '<EMAIL>',
  'PROPOSAL',
  110000.00,
  '2025-08-05',
  NULL,
  'ACTIVE',
  'MEDIUM',
  'COLD_CALL',
  'Review contract terms',
  'Negotiating on content licensing',
  @ORG_ID
),
(
  uuid(),
  'Manufacturing Deal Closed',
  @LEAD_ID_1,
  '<EMAIL>',
  'CLOSED_WON',
  220000.00,
  '2025-05-28',
  '2025-05-27',
  'CLOSED',
  'HIGH',
  'REFERRAL',
  NULL,
  'Long-term contract signed',
  @ORG_ID
),
(
  uuid(),
  'Energy Sector Lost Deal',
  @LEAD_ID_1,
  '<EMAIL>',
  'CLOSED_LOST',
  140000.00,
  '2025-05-12',
  '2025-05-15',
  'CLOSED',
  'LOW',
  'WEBSITE',
  NULL,
  'Budget constraints caused loss',
  @ORG_ID
);
