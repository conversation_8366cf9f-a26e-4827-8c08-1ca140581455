package com.ensar.entity;

import lombok.Data;
import lombok.EqualsAndHashCode;
import jakarta.persistence.*;

@Entity(name = "linkedins")
@Data
@EqualsAndHashCode(callSuper = true)
public class Linkedin extends BaseEntity {

    @Column(name = "account_name", nullable = false, length = 100)
    private String accountName; // Renamed from "name"

    @Column(name = "email", nullable = false, length = 100)
    private String email; // Renamed from "title"

    @Column(name = "password", nullable = false, length = 100)
    private String password; // Renamed from "occupation"

    @Column(name = "designation", nullable = false, length = 255)
    private String designation; // Renamed from "headline"

    @Column(name = "country", nullable = false, length = 100)
    private String country;

    @Column(name = "connections_count", nullable = false)
    private Integer connectionsCount; // Added column

    @Column(name = "status", nullable = false, length = 20)
    private String status; // Added column

    @ManyToOne
    @JoinColumn(name = "handled_by", referencedColumnName = "id",nullable = false)
    private User handledBy;

    @OneToOne
    @JoinColumn(name = "organization_id", referencedColumnName = "id")
    private Organization organization;
}
