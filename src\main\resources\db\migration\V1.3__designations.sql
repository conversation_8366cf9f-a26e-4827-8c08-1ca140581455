DROP TABLE IF EXISTS `designations`;

CREATE TABLE `designations`
(
    `id`                   char(36)     NOT NULL,
    `name`                 VARCHAR(100) NOT NULL,
    `description`          TEXT  ,
    `organization_id` CHAR(36) NOT NULL,
    `created_date_time`    timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_updated_date_time` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    CONSTRAINT fk_designations_organization FOREIGN KEY (organization_id) REFERENCES organizations(id) ON DELETE CASCADE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;