spring:
  application:
    name: demo-svc
  ds:
    url: jdbc:mysql://${DB_SERVER}:${DB_PORT}/${DB_SCHEMA}?createDatabaseIfNotExist=true&zeroDateTimeBehavior=convertToNull&autoReconnect=true&useLegacyDatetimeCode=false&serverTimezone=UTC&verifyServerCertificate=false&useSSL=${DB_USE_SSL}&requireSSL=${DB_REQUIRE_SSL}&only_full_group_by=false&useUnicode=true&characterEncoding=utf8
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    driver-class-name: com.mysql.jdbc.Driver
    testPath-on-borrow: true
    testPath-while-idle: true
    validation-query: SELECT 1
  flyway:
    schemas: ${DB_SCHEMA}
    placeholders:
      schema: ${DB_SCHEMA}
  jpa:
    show-sql: true
    properties:
      hibernate:
        format-sql: true
        dialect: org.hibernate.dialect.MySQL8Dialect
  mail:
      host: smtp.gmail.com
      port: 587
      username: <EMAIL>
      password: cnvnnvdfxihxjvbf
      properties:
        mail:
          smtp:
            auth: true
            starttls:
              enable: true
              required: true
            connectiontimeout: 5000
            timeout: 5000
            writetimeout: 5000

app:
  jwt:
    secret:
      key: ${JWT_SECRET_KEY}
    ttl:
      minutes: ${JWT_TTL_MINS}
  url:
    prefix: ${SERVER_URL_PREFIX:http://localhost:3000}

