package com.ensar.controller;

import com.ensar.entity.Linkedin;
import com.ensar.entity.Organization;
import com.ensar.request.dto.CreateUpdateLinkedinDto;
import com.ensar.security.EnsarUserDetails;
import com.ensar.service.LinkedinService;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ContentDisposition;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

//@Api(tags = "LinkedIn Profile Management")
@RestController
@Validated
@CrossOrigin(origins = "*", maxAge = 3600)
@RequestMapping("/api/linkedin")
@Tag(name = "LinkedIn Management") // Class-level Tag annotation

public class LinkedinController {

    private final LinkedinService linkedinService;

    @Autowired
    public LinkedinController(LinkedinService linkedinService) {
        this.linkedinService = linkedinService;
    }

    @GetMapping("/{id}")
    public ResponseEntity<Linkedin> getLinkedinById(@PathVariable String id) {
        Linkedin linkedin = linkedinService.getLinkedinById(id);
        return ResponseEntity.ok(linkedin);
    }

    @GetMapping("/")
    public ResponseEntity<Map<String, Page<Linkedin>>> getLinkedinProfilesByPagination(@AuthenticationPrincipal EnsarUserDetails userDetails,
                                                                              @RequestParam(defaultValue = "0") int page,
                                                                              @RequestParam(defaultValue = "5") int size) {
        String orgId = userDetails.getOrganization().getId();
        Page<Linkedin> linkedinList = linkedinService.getLinkedinProfilesByPagination(orgId, page, size);
        Map<String, Page<Linkedin>> response = new HashMap<>();
        response.put("profiles", linkedinList);
        return ResponseEntity.ok(response);
    }

    @GetMapping("/all")
    public ResponseEntity<Map<String, List<Linkedin>>> getAllLinkedinProfiles(@AuthenticationPrincipal EnsarUserDetails userDetails) {
        String orgId = userDetails.getOrganization().getId();
        List<Linkedin> linkedinList = linkedinService.getAllLinkedinProfiles(orgId);
        Map<String, List<Linkedin>> response = new HashMap<>();
        response.put("profiles", linkedinList);
        return ResponseEntity.ok(response);
    }

    @PostMapping("/")
    public ResponseEntity<Linkedin> createLinkedin(@Valid @RequestBody CreateUpdateLinkedinDto createUpdateLinkedinDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Linkedin linkedin = linkedinService.createOrUpdateLinkedin(Optional.empty(), createUpdateLinkedinDto,organization);
        return ResponseEntity.ok(linkedin);
    }

    @PutMapping("/{id}")
    public ResponseEntity<Linkedin> updateLinkedin(@PathVariable String id,
                                                   @Valid @RequestBody CreateUpdateLinkedinDto createUpdateLinkedinDto,@AuthenticationPrincipal EnsarUserDetails userDetails) {
        Organization organization = userDetails.getOrganization();
        Linkedin linkedin = linkedinService.createOrUpdateLinkedin(Optional.of(id), createUpdateLinkedinDto,organization);
        return ResponseEntity.ok(linkedin);
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteLinkedin(@PathVariable String id) {
        linkedinService.deleteLinkedin(id);
        return ResponseEntity.ok().build();
    }

    @PostMapping("/import")
    public ResponseEntity<Void> importLinkedinProfiles(@RequestParam("file") MultipartFile file) throws IOException {
        linkedinService.importLinkedinProfiles(file.getInputStream());
        return ResponseEntity.ok().build();
    }

    @GetMapping("/export")
    public ResponseEntity<byte[]> exportLinkedinProfiles() throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        linkedinService.exportLinkedinProfiles(outputStream);
        byte[] bytes = outputStream.toByteArray();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        ContentDisposition contentDisposition = ContentDisposition.builder("attachment")
                .filename("linkedin_profiles.csv")
                .build();
        headers.setContentDisposition(contentDisposition);

        return ResponseEntity.ok()
                .headers(headers)
                .body(bytes);
    }
}
